{"name": "liaoyuan-solution", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "dev:test": "vite --mode test", "dev:prod": "vite --mode production", "build": "set NODE_OPTIONS=--max-old-space-size=4096 && vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.1.0", "@kangc/v-md-editor": "^2.3.18", "@types/marked": "^6.0.0", "@unocss/reset": "^0.59.4", "@vue-office/docx": "^1.6.1", "@vue-office/excel": "^1.7.8", "animate.css": "^4.1.1", "ant-design-vue": "^2.2.8", "axios": "^1.6.8", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.5.0", "element-plus": "^2.8.6", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "json-bigint": "^1.0.0", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-attrs": "^4.3.1", "markdown-it-toc": "^1.1.0", "marked": "^15.0.12", "mitt": "^3.0.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "ranui": "^0.1.9", "recorder-core": "^1.3.24102001", "unocss": "^0.59.4", "uuid": "^10.0.0", "vue": "^3.4.21", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.7", "vue-draggable-next": "^2.2.1", "vue-draggable-plus": "^0.5.3", "vue-lazyload": "^3.0.0", "vue-markdown": "^2.2.4", "vue-router": "^4.3.0", "vue3-print-nb": "^0.1.4", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "sass": "^1.86.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.0"}}