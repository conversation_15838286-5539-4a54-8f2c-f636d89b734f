<template>
  <div class="tab" v-if="showAbility">
    <span
      :class="['ability', { active: activeTab == '1' }]"
      @click="changeTab('1')"
      >能力/方案场景</span
    >
    <!-- <span
      :class="['scence', { active: activeTab == '2' }]"
      @click="changeTab('2')"
      >方案场景</span
    > -->
  </div>
  <div class="searchInfo">
    <div class="vocationPull">
      <a-config-provider
        :locale="zhCN"
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
      >
        <a-input-search
          v-model:value="name"
          placeholder="请输入名称进行检索"
          enter-button="搜索"
          @search="seekContent"
          @keyup.enter="seekContent"
        />
      </a-config-provider>
    </div>
  </div>

  <div class="tabContent">
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="cardContent">
        <div class="card_total">
          <template v-for="(item, index) in tableList" :key="index">
            <div
              :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                  rightActive: index % 2 != 0,
                  cardObvious: index < 2 && tableList.length < 3,
                  bottomLine:
                    (index == tableList.length - 1 ||
                      index == tableList.length - 2) &&
                    index > 1,
                },
              ]"
              @mouseenter="contentColor(index)"
              @mouseleave="contentLeave"
              @click="proDetail(item)"
            >
              <button
                @click="deleteBtn"
                class="cart-button"
                v-if="item.addGroup"
              >
                <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                  &nbsp;已加入</span
                >
              </button>
              <button
                @click="deleteBtn"
                class="cart-button"
                v-if="
                  selectIds.includes(item.id) ||
                  selectAbilityIds.includes(item.id)
                "
              >
                <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                  &nbsp;已选择</span
                >
              </button>

              <button
                class="cart-button pointer"
                @click.stop="add(item)"
                v-if="
                  !item.addGroup &&
                  !selectIds.includes(item.id) &&
                  !item.addGroup &&
                  !selectAbilityIds.includes(item.id)
                "
              >
                <img
                  class="add-icon"
                  src=" @/assets/images/AI/isadded.png"
                /><span class="add"> &nbsp;加入组合</span>
              </button>
              <div style="display: flex; margin: 24px">
                <div>
                  <a-image
                    :width="168"
                    :height="105"
                    :preview="false"
                    v-if="item.picture"
                    :src="`${item.picture}`"
                  />
                  <img
                    src="@/assets/images/home/<USER>"
                    style="width: 168px; height: 105px"
                    v-else
                  />
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <div class="card_title">
                        {{ item.name }}
                      </div>
                      <span class="cityStyle" v-if="item.provider">{{
                        item.provider
                      }}</span>
                    </div>
                  </div>
                  <div class="card_des">
                    {{ item.summary }}
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    ></div>
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    "
                  >
                    <div>
                      <!-- <img
                        style="width: 112px; height: 22px"
                        src="@/assets/images/home/<USER>"
                      /> -->
                    </div>
                    <div style="display: flex; align-items: center">
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px"
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.viewCount !== null"
                        >{{ item.viewCount }}</span
                      >
                      <span v-else>-</span>
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px; margin-left: 18px"
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.downloadCount != null"
                        >{{ item.downloadCount }}</span
                      >
                      <span v-else>-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage">
        <a-pagination
          v-model:pageSize="pageItemSize"
          v-model:current="currentPage"
          :pageSizeOptions="pageSizeOptions"
          show-quick-jumper
          show-size-changer
          :total="totalItemCount"
          @change="pageChange"
          @showSizeChange="sizeChange"
          class="mypage"
        />
      </div>
      <div class="btn_box">
        <span class="refuse" @click="refuse">取消</span>
        <button
          :class="{
            submit: selectNum > 0,
            disabled: selectNum == 0,
          }"
          @click="submit"
          :disabled="selectNum == 0"
        >
          确认添加（已选择{{ selectNum }}个）
        </button>
      </div>
    </div>
    <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { getSceneSchemeList, getProjectList } from "@/api/moduleList/home";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { addCombine } from "@/api/combine/combine.js";
import { guideList } from "@/api/moduleList/home";
import eventBus from "@/utils/eventBus";
import { select } from "ranui";
export default defineComponent({
  components: {},
  props: {
    choseType: {
      type: Number,
      default: null,
    },
    labelId: {
      type: Number,
      default: null,
    },
    showAbility: {
      type: Boolean,
      default: false,
    },
    cover: {
      type: String,
      default: "",
    },
    conclusion: {
      type: String,
      default: "",
    },
  },
  setup(props, { emit }) {
    const vocation = ref("");
    const region = ref("");
    const data = reactive({
      choseType: props.choseType,
      showAbility: props.showAbility,
      labelId: props.labelId,
      name: "",
      moment: "",
      loadingShow: true,
      activeTab: props.showAbility ? "1" : "",
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      selectIds: [],
      selectAbilityIds: [],
      selectNum: 0,
    });
    const getList = () => {
      data.loadingShow = true;
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
        type: data.choseType,
      };
      // 场景合并能力方案
      if (data.choseType == 5) {
        guideList(pageParams)
          .then((res) => {
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
              item.summary = item.introduce;
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((err) => {
            data.loadingShow = false;
          });
      } else {
        if (data.choseType == 8) {
          pageParams.industryId = data.labelId;
          pageParams.addGroup = false;
        }
        getSceneSchemeList(pageParams)
          .then((res) => {
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
              item.picture = item.image;
              if (item.type == 8) {
                item.summary = item.projectIntroduction;
                item.name = item.projectName;
              }
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      }
    };
    getList();
    eventBus.on("moduleRefresh", getList);
    const seekContent = () => {
      data.currentPage = 1;
      getList();
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      // router.push({
      //   query: {
      //     id: val.id,
      //   },
      //   name: "modulelNew",
      // });
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (item) => {
      // classify=2场景，1能力
      if (item.classify == 1) {
        data.selectAbilityIds.push(item.id);
      } else {
        data.selectIds.push(item.id);
      }
      data.selectNum = data.selectAbilityIds.length + data.selectIds.length;
      getList();
    };
    const refuse = () => {
      data.selectIds = [];
      data.selectAbilityIds = [];
      emit("close");
    };
    const submit = () => {
      const schemes = data.selectIds.map((item) => {
        return {
          schemeId: item,
          classify: "2",
          cover: props.cover,
          conclusion: props.conclusion,
        };
      });
      const scheme = data.selectAbilityIds.map((item) => {
        return {
          schemeId: item,
          classify: "1",
          cover: props.cover,
          conclusion: props.conclusion,
        };
      });
      data.selectIds = [];
      data.selectAbilityIds = [];
      schemes.push(...scheme);
      addCombine(schemes).then((res) => {
        emit("close");
      });
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      getList();
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      getList();
    };
    const deleteBtn = (id) => {
      const index = data.selectIds.indexOf(id);
      if (index > -1) {
        data.selectIds.splice(index, 1);
      }
    };
    watch(
      () => props.choseType,
      (newV) => {
        data.choseType = newV;
      }
    );
    const changeTab = (val) => {
      data.activeTab = val;
      data.currentPage = 1;
      getList();
    };
    return {
      ...toRefs(data),
      vocation,
      changeTab,
      region,
      add,
      contentColor,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      deleteBtn,
      refuse,
      seekContent,
      submit,
    };
  },
});
</script>

<style lang="scss" scoped src="./choseTable.scss"></style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}
</style>