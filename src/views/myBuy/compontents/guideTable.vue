<template>
  <div>
    <div v-if="status === 1">
      <div class="orderHeard">
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">1</div>
          <div class="orderHeard-first-name">填写客户信息</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">2</div>
          <div class="orderHeard-second-name">添加场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">3</div>
          <div class="orderHeard-second-name">添加产品</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">4</div>
          <div class="orderHeard-second-name">填写客户经理信息</div>
        </div>
      </div>
      <div class="tabContent">
        <div style="width: 100%">
          <div class="cardContent">
            <div class="card_total">
              <a-form ref="formRef" :model="formState" :rules="rules">
                <a-form-item
                  ref="enterpriseName"
                  label="企业名称"
                  name="enterpriseName"
                >
                  <a-input
                    v-model:value="formState.enterpriseName"
                    placeholder="请输入企业名称"
                  /> </a-form-item
                ><a-form-item
                  ref="enterpriseScale"
                  label="企业规模"
                  name="enterpriseScale"
                  style="margin-left: 12px;"
                >
                  <a-input
                    v-model:value="formState.enterpriseScale"
                    placeholder="请输企业规模"
                  />
                </a-form-item>
                <a-form-item
                  ref="customerName"
                  label="联系人"
                  name="customerName"
                  style="margin-left: 15px;"
                >
                  <a-input
                    v-model:value="formState.customerName"
                    placeholder="请输入联系人名称"
                  />
                </a-form-item>
                <a-form-item
                  ref="contactNumber"
                  label="联系电话"
                  name="contactNumber"
                >
                  <a-input
                    v-model:value="formState.contactNumber"
                    placeholder="请输入联系电话"
                  />
                </a-form-item>
                <a-form-item
                  ref="address"
                  label="联系地址"
                  name="address"
                  style="margin-left: 15px;"
                >
                  <a-input
                    v-model:value="formState.address"
                    placeholder="请输入联系地址"
                  />
                </a-form-item>
                <a-form-item
                  label="所属行业"
                  name="industry"
                  style="margin-left: 15px;"
                >
                  <a-select
                    placeholder="请选择所属行业"
                    v-model:value="formState.industry"
                    style="width: 1015px"
                  >
                    <template v-for="(opt, index) in typeList" :key="index">
                      <a-select-option :value="opt.name">
                        {{ opt.name }}
                      </a-select-option>
                    </template>
                  </a-select>
                </a-form-item>
              </a-form>
            </div>
          </div>
          <div class="btn_box">
            <span class="jump"></span>
            <div>
              <span class="refuse" @click="refuse">取消</span>
              <span class="submit" @click="submit">下一步</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="status === 2">
      <div class="orderHeard">
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img
              src="@/assets/images/AI/achieve.png"
              style="width: 32px; height: 32px"
              alt=""
            />
          </div>
          <div class="orderHeard-third-name">填写客户信息</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">2</div>
          <div class="orderHeard-first-name">添加场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">3</div>
          <div class="orderHeard-second-name">添加产品</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">4</div>
          <div class="orderHeard-second-name">填写客户经理信息</div>
        </div>
      </div>
      <div class="searchInfo">
        <div class="vocationPull">
          <a-config-provider
            :locale="zhCN"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          >
            <a-input-search
              v-model:value="name"
              placeholder="请输入方案模块名称、描述、关键字检索"
              enter-button="搜索"
              @search="seekContent"
              @keyup.enter="seekContent"
            />
          </a-config-provider>
        </div>
      </div>
      <div class="tabContent">
        <div v-if="tableList && tableList.length > 0" style="width: 100%">
          <div class="cardContent">
            <div class="card_total">
              <template v-for="(item, index) in tableList" :key="index">
                <div
                  :class="[
                    'card_content',
                    {
                      cardActive: cardActive == index,
                      rightActive: index % 2 != 0,
                      cardObvious: index < 2 && tableList.length < 3,
                      bottomLine:
                        (index == tableList.length - 1 ||
                          index == tableList.length - 2) &&
                        index > 1,
                      selectBorder: schemeSelectIds.includes(item.id),
                    },
                  ]"
                  @mouseenter="contentColor(index)"
                  @mouseleave="contentLeave"
                  @click="proDetail(item)"
                >
                  <button
                    class="cart-button pointer"
                    @click.stop="add(item)"
                    v-if="!schemeSelectIds.includes(item.id)"
                  >
                    <img
                      class="add-icon"
                      src=" @/assets/images/AI/isadded.png"
                    />
                    <span class="add"> &nbsp;加入预选</span>
                  </button>
                  <button
                    class="cart-button pointer"
                    @click.stop="outScheme(item)"
                    v-else
                  >
                    <img
                      class="add-icon"
                      src=" @/assets/images/AI/shiftOut.png"
                    />
                    <span class="shiftOut"> &nbsp;移出订购</span>
                  </button>
                  <div style="display: flex; margin: 24px">
                    <div>
                      <a-image
                        :width="168"
                        :height="105"
                        :preview="false"
                        v-if="item.mainImg"
                        :src="`${item.mainImg}`"
                      />
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 168px; height: 105px"
                        v-else
                      />
                    </div>
                    <div class="card_center">
                      <div class="card_text">
                        <div class="card_tag flex" style="align-items: center">
                          <span
                            class="cardTag"
                            style="background-color: #d7e6ff; color: #2e7fff"
                            >{{ item.classifyName }}</span
                          >
                          <div class="card_title">{{ item.name }}</div>
                        </div>
                      </div>
                      <!-- <span>{{ item.labelName }}</span> -->
                      <div class="card_des">
                        {{ item.introduce }}
                      </div>
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        "
                      >
                        <div style="display: flex; align-items: center">
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.viewCount"
                            >{{ item.viewCount }}</span
                          >
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-else
                            >0</span
                          >
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px; margin-left: 18px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.downloadCount"
                            >{{ item.downloadCount }}</span
                          >
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-else
                            >0</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="layPage">
            <a-pagination
              v-model:pageSize="pageItemSize"
              v-model:current="currentPage"
              :pageSizeOptions="pageSizeOptions"
              show-quick-jumper
              show-size-changer
              :total="totalItemCount"
              @change="pageChange"
              @showSizeChange="sizeChange"
              class="mypage"
            />
          </div>
          <div class="btn_box">
            <span class="jump"></span>
            <div>
              <span class="refuse" @click="reLast">上一步</span>
              <span class="submit" @click="submit"
                >下一步（已选择{{ schemeSelectIds.length }}个）</span
              >
            </div>
          </div>
        </div>
        <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
    </div>
    <div v-if="status === 3">
      <div class="orderHeard">
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img
              src="@/assets/images/AI/achieve.png"
              style="width: 32px; height: 32px"
              alt=""
            />
          </div>
          <div class="orderHeard-third-name">填写客户信息</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img
              src="@/assets/images/AI/achieve.png"
              style="width: 32px; height: 32px"
              alt=""
            />
          </div>
          <div class="orderHeard-third-name">添加场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">3</div>
          <div class="orderHeard-first-name">添加产品</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">4</div>
          <div class="orderHeard-second-name">填写客户经理信息</div>
        </div>
      </div>
      <div class="searchInfo">
        <div class="vocationPull">
          <a-config-provider
            :locale="zhCN"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          >
            <a-input-search
              v-model:value="name"
              placeholder="请输入方案模块名称、描述、关键字检索"
              enter-button="搜索"
              @search="seekContent"
              @keyup.enter="seekContent"
            />
          </a-config-provider>
        </div>
      </div>
      <div class="tabContent">
        <div v-if="tableList && tableList.length > 0" style="width: 100%">
          <div class="cardContent">
            <div class="card_total">
              <template v-for="(item, index) in tableList" :key="index">
                <div
                  :class="[
                    'card_content',
                    {
                      cardActive: cardActive == index,
                      rightActive: index % 2 != 0,
                      cardObvious: index < 2 && tableList.length < 3,
                      bottomLine:
                        (index == tableList.length - 1 ||
                          index == tableList.length - 2) &&
                        index > 1,
                      selectBorder: programmeSelectIds.includes(item.id),
                    },
                  ]"
                  @mouseenter="contentColor(index)"
                  @mouseleave="contentLeave"
                  @click="proDetail(item)"
                >
                  <button
                    class="cart-button pointer"
                    @click.stop="add(item)"
                    v-if="!programmeSelectIds.includes(item.id)"
                  >
                    <img
                      class="add-icon"
                      src=" @/assets/images/AI/isadded.png"
                    />
                    <span class="add"> &nbsp;加入预选</span>
                  </button>
                  <button
                    class="cart-button pointer"
                    @click.stop="outProgramme(item)"
                    v-else
                  >
                    <img
                      class="add-icon"
                      src=" @/assets/images/AI/shiftOut.png"
                    />
                    <span class="shiftOut"> &nbsp;移出订购</span>
                  </button>
                  <div style="display: flex; margin: 24px">
                    <div>
                      <a-image
                        :width="168"
                        :height="105"
                        :preview="false"
                        v-if="item.image"
                        :src="`${item.image}`"
                      />
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 168px; height: 105px"
                        v-else
                      />
                    </div>
                    <div class="card_center">
                      <div class="card_text">
                        <div class="card_tag flex" style="align-items: center">
                          <span
                            class="cardTag"
                            style="background-color: #d7e6ff; color: #2e7fff"
                            >{{ item.labelName }}</span
                          >
                          <div class="card_title">{{ item.name }}</div>
                        </div>
                      </div>
                      <div class="card_des">
                        {{ item.introduction }}
                      </div>
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        "
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        ></div>
                      </div>
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        "
                      >
                        <div style="display: flex; align-items: center">
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.viewCount"
                            >{{ item.viewCount }}</span
                          >
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-else
                            >0</span
                          >
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px; margin-left: 18px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.downloadCount"
                            >{{ item.downloadCount }}</span
                          >
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-else
                            >0</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="layPage">
            <a-pagination
              v-model:pageSize="pageItemSize"
              v-model:current="currentPage"
              :pageSizeOptions="pageSizeOptions"
              show-quick-jumper
              show-size-changer
              :total="totalItemCount"
              @change="pageChange"
              @showSizeChange="sizeChange"
              class="mypage"
            />
          </div>
          <div class="btn_box">
            <span class="jump"></span>
            <div>
              <span class="refuse" @click="reLast">上一步</span>
              <span class="submit" @click="submit"
                >下一步（已选择{{ programmeSelectIds.length }}个）</span
              >
            </div>
          </div>
        </div>
        <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
    </div>
    <div v-if="status === 4">
      <div class="orderHeard">
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img
              src="@/assets/images/AI/achieve.png"
              style="width: 32px; height: 32px"
              alt=""
            />
          </div>
          <div class="orderHeard-third-name">填写客户信息</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img
              src="@/assets/images/AI/achieve.png"
              style="width: 32px; height: 32px"
              alt=""
            />
          </div>
          <div class="orderHeard-third-name">添加场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img
              src="@/assets/images/AI/achieve.png"
              style="width: 32px; height: 32px"
              alt=""
            />
          </div>
          <div class="orderHeard-third-name">添加产品</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">4</div>
          <div class="orderHeard-first-name">填写客户经理信息</div>
        </div>
      </div>
      <div class="tabContent">
        <div style="width: 100%">
          <div style="height: 500px; overflow: hidden auto">
            <div>
              <div style="margin-bottom: 10px">
                <span class="title">客户经理信息</span>
              </div>
              <div class="cardContent">
                <div class="card_total" style="height: auto">
                  <a-form
                    :model="formState"
                    style="width: 98%; margin-left: 2%"
                    id="form1"
                    ref="formRefFront"
                    :rules="rules"
                  >
                    <a-form-item label="客户经理" name="customerManager">
                      <a-input
                        v-model:value="formState.customerManager"
                        placeholder="请输入客户经理名称"
                      />
                    </a-form-item>
                    <a-form-item label="联系电话" name="managerNumber">
                      <a-input
                        v-model:value="formState.managerNumber"
                        placeholder="请输入联系电话"
                      />
                    </a-form-item>
                    <a-form-item label="生成日期" name="contractDate">
                      <a-date-picker
                        v-model:value="formState.contractDate"
                        :value-format="'YYYY-MM-DD'"
                        placeholder="请选择生成日期"
                      />
                    </a-form-item>
                  </a-form>
                </div>
              </div>
            </div>
          </div>
          <div class="btn_box">
            <span class="jump"></span>
            <div>
              <span class="refuse" @click="reLast">上一步</span>
              <span class="submit" @click="putIn">去封装</span>
            </div>
          </div>
        </div>
        <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
    </div>
    <a-modal v-model:visible="visible" title="提示" @ok="handleOk">
      <p>已存在订购数据，请确认是否替换已有组合?</p>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { message } from "ant-design-vue";
import { myCombineList } from "@/api/combine/combine.js";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { toGuidePage } from "@/api/combine/shoppingCart.js";
import { getProductList } from "@/api/product/home";
import eventBus from "@/utils/eventBus";
import { getTradeList } from "@/api/solutionNew/home";
import { getSceneList } from "@/api/scenario/home";
import { useHomeStore } from "@/store";
import { getDateTime } from "@/utils/index.js";
import { toShopList } from "@/api/buyList/index.js";
export default defineComponent({
  components: {},
  props: {},
  setup(props, { emit }) {
    const vocation = ref("");
    const counterStore = useHomeStore();
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    const region = ref("");
    const formRefFront = ref();
    const formRefFinal = ref();
    const data = reactive({
      name: "",
      moment: "",
      visible: false,
      loadingShow: true,
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      status: 1,
      schemeSelectIds: [],
      schemeSelectItem: [],
      programmeSelectIds: [],
      programmeSelectItem: [],
      selectId: null,
      solveId: {},
      selectIds: [],
      selectLists: [],
      formState: {
        customerName: "",
        contactNumber: "",
        enterpriseScale: "",
        industry: undefined,
        address: "",
        enterpriseName: "",
        customerManager: userInfo.realName,
        managerNumber: userInfo.phone,
        contractDate: getDateTime("date"),
      },
      tableList: [],
      typeList: [],
    });
    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
      };
      if (data.status === 3) {
        getProductList(pageParams)
          .then((res) => {
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
              item.label = item.label.split(",");
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      } else if (data.status === 2) {
        getSceneList(pageParams)
          .then((res) => {
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
              item.label = item.label.split(",");
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      }
      data.loadingShow = true;
    };
    getList();
    eventBus.on("moduleRefresh", getList);
    const seekContent = () => {
      getList();
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (item) => {
      let isHave = false;
      if (data.status == 1) {
        for (let i in data.schemeSelectIds) {
          if (data.schemeSelectIds[i] == item.id) {
            isHave = true;
            break;
          }
        }
        if (!isHave) {
          add(item);
        } else {
          outScheme(item);
        }
      } else if (data.status == 2) {
        for (let i in data.programmeSelectIds) {
          if (data.programmeSelectIds[i] == item.id) {
            isHave = true;
            break;
          }
        }
        if (!isHave) {
          add(item);
        } else {
          outProgramme(item);
        }
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (item) => {
      if (data.status === 2) {
        data.schemeSelectIds.push(item.id);
        data.schemeSelectItem.push(item);
        data.selectLists.push({
          type: 1,
          productId: item.id,
        });
        // addShop({ productId: item.id, type: 1 });
      } else if (data.status === 3) {
        data.programmeSelectIds.push(item.id);
        data.programmeSelectItem.push(item);
        // addShop({ productId: item.id, type: 2 });
        data.selectLists.push({
          type: 2,
          productId: item.id,
        });
      }
    };

    const outScheme = (item) => {
      data.schemeSelectIds = data.schemeSelectIds.filter((item1, index1) => {
        return item1 !== item.id;
      });
      data.schemeSelectItem = data.schemeSelectItem.filter((item1, index1) => {
        return item1.id !== item.id;
      });
      for (let i = 0; i < data.selectLists.length; i++) {
        const ele = data.selectLists[i];
        if (ele.type === 1 && ele.productId === item.id) {
          data.selectLists.splice(i, 1);
          break; // 找到匹配项后立即跳出循环
        }
      }
      // deleteShop([{ productId: item.id, type: 1 }]);
    };

    const outProgramme = (item) => {
      data.programmeSelectIds = data.programmeSelectIds.filter(
        (item1, index1) => {
          return item1 !== item.id;
        }
      );
      data.programmeSelectItem = data.programmeSelectItem.filter(
        (item1, index1) => {
          return item1.id !== item.id;
        }
      );
      for (let i = 0; i < data.selectLists.length; i++) {
        const ele = data.selectLists[i];
        if (ele.type === 2 && ele.productId === item.id) {
          data.selectLists.splice(i, 1);
          break; // 找到匹配项后立即跳出循环
        }
      }
      // deleteShop([{ productId: item.id, type: 1 }]);
    };
    const getModuId = (item) => {
      let isHave = false;
      for (let i in data.selectIds) {
        if (data.selectIds[i] == item.id) {
          isHave = true;
          break;
        }
      }
      if (!isHave) {
        data.selectLists.push({
          type: 2,
          schemeId: item.id,
        });
        data.selectIds.push(item.id);
      } else {
        data.selectLists = data.selectLists.filter(
          (item1) => item1.schemeId != item.id
        );
        data.selectIds = data.selectIds.filter((item1) => item1 != item.id);
      }
    };

    const refuse = () => {
      data.schemeSelectIds = [];
      data.schemeSelectItem = [];
      data.programmeSelectIds = [];
      data.programmeSelectItem = [];
      data.formState = {
        customerName: "",
        contactNumber: "",
        enterpriseScale: "",
        industry: undefined,
        address: "",
        enterpriseName: "",
        customerManager: userInfo.realName,
        managerNumber: userInfo.phone,
        contractDate: getDateTime("date"),
      };
      emit("close");
    };
    const pase = () => {
      emit("close");
    };
    //清空
    const empty = () => {
      data.schemeSelectIds = [];
      data.schemeSelectItem = [];
      data.programmeSelectIds = [];
      data.programmeSelectItem = [];
      data.formState = {
        customerName: "",
        contactNumber: "",
        enterpriseScale: "",
        industry: undefined,
        address: "",
        enterpriseName: "",
        customerManager: userInfo.realName,
        managerNumber: userInfo.phone,
        contractDate: getDateTime("date"),
      };
    };
    //上一步
    const reLast = () => {
      data.status -= 1;
      data.currentPage = 1;
      if (data.status <= 2) {
        getList();
      } else if (data.status == 3) {
        // showSelectList();
      } else {
      }
    };
    //下一步
    const submit = () => {
      if (data.status === 1) {
        formRef.value.validate().then((valid) => {
          if (valid) {
            // 表单验证成功后的逻辑
            data.status += 1;
            if (data.status <= 3) {
              data.currentPage = 1;
              getList();
            }
          } else {
            // 表单验证失败的处理
            message.error("请填写正确的信息");
            return false;
          }
        });
      } else if (data.status === 2) {
        // 如果需要，可以在这里添加状态为2时的逻辑
        data.status += 1;
        data.currentPage = 1;
        getList();
      } else if (data.status === 3) {
        if (
          data.schemeSelectIds.length === 0 &&
          data.programmeSelectIds.length === 0
        ) {
          message.warning("请至少选择一个产品或场景进行封装");
          return false;
        } else {
          // 处理选择后的逻辑
          data.status += 1;
          // 可能需要的后续操作
        }
      } else {
        // 超出预期的状态处理
        message.error("发生未知错误，请重试");
        return false;
      }
    };
    const putIn = () => {
      let productShoppingCarts = [...data.selectLists];
      let params = {
        productShoppingCarts: productShoppingCarts,
        ...data.formState,
        source: "2",
      };
      toShopList(params)
        .then((res) => {
          data.schemeSelectIds = [];
          data.schemeSelectItem = [];
          data.programmeSelectIds = [];
          data.programmeSelectItem = [];
          data.selectLists = [];
          data.formState = {
            customerName: "",
            contactNumber: "",
            enterpriseScale: "",
            industry: undefined,
            address: "",
            enterpriseName: "",
            customerManager: userInfo.realName,
            managerNumber: userInfo.phone,
            contractDate: getDateTime("date"),
          };
          data.show = false;
          emit("close");
          router.push({
            query: {
              type: 10,
            },
            name: "newAllProject",
          });
          counterStore.buyListStroe = {};
          data.status = 1;
        })
        .catch((error) => {});
    };

    const handleOk = () => {
      data.selectLists.push(data.solveId);
      data.selectLists = data.selectLists.filter((item1) => item1.schemeId);
      let guideData = {
        list: data.selectLists,
      };
      toGuidePage(guideData).then((res) => {
        if (res.code == 200) {
          data.solveId = {};
          data.selectLists = [];
          data.valueId = null;
          data.movalue = [];
          counterStore.buyListStroe = {};
          data.status = 1;
          data.visible = false;
          emit("close");
        }
      });
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      getList();
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      getList();
    };
    const rules = {
      customerName: [
        { required: true, message: "请输入联系人名称", trigger: "blur" },
      ],
      contactNumber: [
        { required: true, message: "请输入联系电话", trigger: "blur" },
        { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: "请正确输入手机号码" },
      ],
      enterpriseName: [
        { required: true, message: "请输入企业名称", trigger: "blur" },
      ],
    };
    const formRef = ref();
    const getTypeList = () => {
      getTradeList()
        .then((res) => {
          data.typeList = res.data;
        })
        .catch((error) => {
          console.error("获取行业列表失败：", error);
        });
    };
    getTypeList();
    return {
      ...toRefs(data),
      vocation,
      counterStore,
      getTypeList,
      rules,
      getDateTime,
      region,
      formRefFront,
      formRefFinal,
      formRef,
      add,
      outScheme,
      outProgramme,
      contentColor,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      pase,
      empty,
      refuse,
      reLast,
      seekContent,
      submit,
      putIn,
      getModuId,
      handleOk,
    };
  },
});
</script>

<style lang="scss" scoped src="./guideTable.scss"></style>

<style lang="scss" scoped>
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }
  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }
  .ant-pagination-item-active {
    background: #007eff;
  }
  .ant-pagination-item-active a {
    color: #ffffff;
  }
  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }
  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }
  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }
  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }
  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;
    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }
  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }
  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
:deep(.ant-form-item-label) {
  width: auto !important;
}
.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}
</style>