<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">行业方案</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
        <div @click="collectById" :class="{ active: collectActive }">
          <img width="22px" height="22px" v-if="detailData.collect == 0"
            src="@/assets/images/solution/detail/notCollect.png" />
          <img v-else width="22px" height="22px" src="@/assets/images/solution/detail/isCollect.png" />
        </div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="left_tit">
              <p>{{ detailData.name }}</p>
              <!--<div class="score">
                <div class="scoreIcon"><img src="../../../assets/images/score.png" alt="" /></div>
                <span class="scoreTitle">方案评分：</span>
                <span class="scoreNum">{{ ecologyDeal(detailData.score) }}</span>
              </div>-->
            </div>
            <div class="left_middle">
              <!-- <p class="info">
                {{ detailData.description }}
              </p> -->
              <a-tooltip overlayClassName="tooltip_class">
                <template v-if="isShowToolTip(detailData.description, 174)" #title>
                  {{ detailData.description }}</template>
                <p class="info">{{ detailData.description }}</p>
              </a-tooltip>
              <div class="bottom1" style="display: block">
                <div class="label flex" style="display: inline-block; margin-right: 24px">
                  <span style="margin-right: 6px" v-for="(item, key) in detailData.labelName" :key="key">{{ item
                  }}</span>
                </div>
                <div style="margin-left: 0; display: inline-block" class="tips">
                  <p v-if="detailData.phone != null">
                    <img src="@/assets/images/solution/detail/eyes.png" alt="" />
                    {{ detailData.viewCount }}
                  </p>
                  <p v-else>
                    <img src="@/assets/images/solution/detail/eyes.png" alt="" />
                    -
                  </p>
                </div>
                <div style="display: inline-block" class="tips">
                  <p v-if="detailData.downloadCount != null">
                    <img src="@/assets/images/solution/detail/left_down.png" alt="" />
                    {{ detailData.downloadCount }}
                  </p>
                  <p v-else>
                    <img src="@/assets/images/solution/detail/left_down.png" alt="" />-
                  </p>
                </div>
                <div style="display: inline-block" class="tips">
                  <p v-if="detailData.collectCount != null">
                    <img src="@/assets/images/solution/detail/star.png" alt="" />
                    {{ detailData.collectCount }}
                  </p>
                  <p v-else>
                    <img src="@/assets/images/solution/detail/star.png" alt="" />
                    -
                  </p>
                </div>
                <div class="info_bottom" style="display: flex; margin-left: 0">
                  <p v-if="detailData.contact != null">
                    联系人：{{ detailData.contact }}
                  </p>
                  <p v-else>联系人：-</p>
                  <p v-if="detailData.phone != null">
                    联系电话：{{ detailData.phone }}
                  </p>
                  <p v-else>联系电话：-</p>
                  <p v-if="detailData.mail != null">
                    联系邮箱：{{ detailData.mail }}
                  </p>
                  <p v-else>联系邮箱：-</p>
                </div>
                <div class="info_bottom" style="display: flex; margin-left: 0">
                  <p>提供方： {{ detailData.provider }}</p>
                  <p>首次上架： {{ detailData.shelfTime ? shelfTimeWith(detailData.shelfTime) : detailData.createTime }}
                  </p>
                  <p>最近更新：
                    {{ detailData.editTime ? shelfTimeWith(detailData.editTime) : shelfTimeWith(detailData.shelfTime) }}
                  </p>
                </div>
                <div class="info_bottom" style="display: flex; margin-left: 0">
                  <p>是否为解耦方案：{{ detailData.isDecoupling == 1 ? '是' : '否' }}</p>
                  <p>
                  方案分类：
                  <span v-if="detailData.ecologyType == 1">自有方案</span>
                  <span v-if="detailData.ecologyType == 2">生态方案</span>
                  <span
                    v-if="
                      detailData.ecologyType &&
                      detailData.ecologyType.split(',').length == 2
                    "
                    >自有方案+生态方案</span
                  >
                </p>
                	<p>是否为敏感方案：{{ detailData.isSensitive == 1 ? '是' : '否' }}</p>
                </div>

                <div class="addCar" v-if="detailData.isSensitive == 0">
                  <button v-if="detailData.addCart" class="disabled">
                    已加入
                  </button>
                  <button v-else @click="add">加入预选</button>
                  <!-- <button v-if="roleKey?.includes('cityIndustryManager')" style="margin-left: 16px;" @click="applyProgram">发起工单</button> -->
                  <button v-if="showApply" @click="apply" style="margin-left: 16px;">申请支撑</button>
                  <!-- <button style="margin-left: 20px;" @click="addAI">去定制</button> -->
                </div>
              </div>
              <div style="display: block; margin-left: 0; margin-bottom: 8px"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="anchors">
        <a-anchor direction="horizontal" :affix="false" v-for="(item, key) in anchorList" @click="handleClick" :key="key">
          <a-anchor-link :class="{ currentActive: isActive === key }" @click="change(key)" :href="item.href"
            :title="item.title" />
        </a-anchor>
      </div>
      <div class="content" id="anchorContent">
        <div v-for="(file, index) in detailData.videoList" :key="index"
          style="margin-bottom: 56px;display: flex;justify-content: center;" :style="videoData.style">
          <video id="video" ref="videoRef" :style="videoData.style" controls :src="file.url" />
        </div>
        <div class="card applyCard" :id="cardItem.href" v-for="cardItem in anchorList" :key="cardItem.title">
          <div v-for="(item, index) in valueData" :key="index">
            <div class="card_content" style="padding-top: 0px; display: block" v-if="item.type == cardItem.type">
              <div class="tab_content">
                <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
                <div class="tit">{{ cardItem.title }}</div>
                <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
              </div>
              <div class="cards">
                <div v-if="item.type == 5" class="apply_list">
                  <div v-for="(value, key) in item.moduleList" :key="key" class="oneStyle">
                    <div class="list_top flex">
                      <div class="top_left">
                        <img v-if="value.image" v-lazy="`${value.image}`" alt="" />
                        <img v-else src="@/assets/images/solution/detail/noneData.png" alt="" />
                      </div>
                      <div class="top_right">
                        <div class="tit" @click="toDetail(value)">
                          {{ value.name }}
                        </div>
                        <a-tooltip overlayClassName="tooltip_class">
                          <template v-if="isShowToolTip(value.summary, 246)" #title>
                            {{ value.summary }}
                          </template>
                          <span class="con">{{ value.summary }}</span>
                        </a-tooltip>
                        <div class="file flex" style="margin-top: 16px" v-if="value.fileName && detailData.isDecoupling == 1">
                          <img src="@/assets/images/solution/detail/text.png" alt=""
                            style="width: 40px; height: 40px" />
                          <p @click="fileShow(value.filePath)" style="font-size:14px;color: #236CFF">
                            {{ fileNameWith(value) }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else-if="item.type == 0" class="provider_con" style="width: 1200px;">
                  <div>
                    <div class="pro" v-for="(value, key) in item.moduleList" :key="key" @click="goHtml(value)"
                    	:style="{'cursor': value.sync==1&&value.auth==1 ? 'pointer' : 'default'}"
                    	>
                      <div class="score" v-if="value.sync==1&&value.auth==1 && value.type ==2">
                        <img class="scoreIcon" src="@/assets/images/score.png" alt="" />
                        <div class="scoreBody" v-if="value.totalScore || value.introScore">
                          <div class="scoreTitle">生态评分：</div>
                          <div class="scoreNum">{{ ecologyDeal(value.totalScore || value.introScore) }}</div>
                        </div>
                        <div class="scoreBody" v-else>
                          <div class="scoreNum" style="font-size: 14px;margin-left: 10px;">暂无评分</div>
                        </div>
                      </div>
                      <div class="score" v-if="(value.sync!=1 || value.auth!=1) && value.type==2">
                        <img class="scoreIcon" src="@/assets/images/scoreNo.png" alt="" />
                        <div class="scoreBody">
                        	<div class="scoreNo">未认证</div>
                        </div>
                      </div>
                      <a-tooltip>
                        <template #title v-if="value.sync==1&&value.auth==1">点击查看详情</template>
                        <div class="flex align-center" style="margin-bottom: 10px;">
                          <img style="width: 26px;height: 26px;margin-right: 8px;"
                            src="@/assets/images/solution/detail/pro_icon.png" alt="" v-if="value.type ==2" />
                          <img style="width: 26px;height: 26px;margin-right: 8px;"
                            src="@/assets/images/solution/detail/pro_own.png" alt="" v-else />
                          <div v-if="value.type ==2" style="font-weight: 400;font-size: 16px;line-height: 28px;color: #2E3852;margin-right: 10px;">
                          	生态合作方：{{ dealData(value.ecopartnerName) }}
                          </div>
                          <div v-if="value.type ==1" style="font-weight: 400;font-size: 16px;line-height: 28px;color: #2E3852;margin-right: 10px;">
                          	自有能力方：{{ dealData(value.ecopartnerName) }}
                          </div>
                        </div>
                        <div class="flex align-center" style="margin-left: 34px;">
                          <div style="width: 24%;font-weight: 400;font-size: 16px;line-height: 28px;color: #2E3852;">
                          	联系人：{{ dealData(value.children[0].ecologyContact) }}
                          </div>
                          <div style="width: 34%;font-weight: 400;font-size: 16px;color: #2E3852;line-height: 28px;">
                          	联系方式：{{ dealData(value.children[0].ecologyPhone) }}
                          </div>
                          <div style="width: 34%;font-weight: 400;font-size: 16px;color: #2E3852;line-height: 28px;">
                            <div style="width:340px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                              <a-tooltip overlayClassName="tooltip_class">
                                <template v-if="isShowToolTip(dealData(value.children[0].contactAddress), 14)"
                                  #title>{{ dealData(value.children[0].contactAddress) }}
                                </template>
                                负责区域：{{ dealData(value.children[0].contactAddress || '江苏省') }}
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-tooltip>
                    </div>
                  </div>
                </div>
                <div v-else-if="item.type == 8" class="apply_list">
                  <div v-for="(value, key) in item.moduleList" :key="key" class="oneStyle">
                    <div class="list_top flex">
                      <div class="top_left">
                        <img v-if="value.image" v-lazy="`${value.image}`" alt="" />
                        <img v-else src="@/assets/images/solution/detail/noneData.png" alt="" />
                      </div>
                      <div class="top_right">
                        <div class="con" v-if="item.type == 8">
                        <p class="right_name">项目名称：{{ value.projectName }}</p>
                        <p class="right_name" v-if="value.scale && value.scale != ''">规模：{{ value.scale }}（万元）</p>
                        <p class="right_name" v-if="value.time && value.time != ''">时间：{{ value.time }}</p>
                        <a-tooltip overlayClassName="tooltip_class">
                          <template v-if="isShowToolTip(value.projectIntroduction, 136)" #title>
                            {{ value.projectIntroduction }} </template>
                          <span class="right_name caseinfo">{{ value.projectIntroduction }}</span>
                        </a-tooltip>
                        </div>
                        <div class="file flex" style="margin-top: 16px" v-if="value.fileName && detailData.isDecoupling == 1">
                          <img src="@/assets/images/solution/detail/text.png" alt=""
                            style="width: 40px; height: 40px" />
                          <p @click="fileShow(value.filePath)" style="font-size:14px;color: #236CFF">
                            {{ fileNameWith(value) }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="oneCards" v-else>
                  <img v-if="
                    item.moduleList[0].image == '' ||
                    item.moduleList[0].image == null
                  " src="../../../assets/images/ability/adlityDetail/bac.png" alt="" />
                  <img v-else v-lazy="`${item.moduleList[0].image}`" alt="" />
                  <div class="right">
                    <a-tooltip overlayClassName="tooltip_class">
                      <template v-if="toolTipShow(item.moduleList, 206)" #title>
                        <div>
                          <div v-for="(module, index) in item.moduleList" :key="index">
                            {{ module.summary }}
                          </div>
                        </div>
                      </template>
                      <div class="desc">
                        <div v-for="(module, index) in item.moduleList" :key="index">
                          {{ module.summary }}
                        </div>
                      </div>
                    </a-tooltip>

                    <div class="fileFlex">
                      <div v-for="(val, idx) in item.moduleList" :key="idx">
                        <div v-if="val.fileName && detailData.isDecoupling == 1" class="file flex" style="margin-top: 16px">
                          <img src="@/assets/images/solution/detail/text.png" alt=""
                            style="width: 40px; height: 40px" />
                          <p @click="fileShow(val.filePath)" style="font-size:14px;color: #236CFF">
                            {{ fileNameWith(val) }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tab_content" id="#download" v-if="detailData.fileList && detailData.fileList.length > 0">
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">方案附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list" v-if="detailData.solutionFileList && detailData.solutionFileList.length > 0">
          <li v-for="(item, key) in detailData.solutionFileList" class="li" :key="key">
            <div class="li_box" @click="fileShow(item.path)">
              <div class="left_box">
                <img src="../../../assets/images/solution/detail/word.png" alt="" style="width: 40px; height: 40px" />
                <p class="fileText">{{ item.name }}</p>
              </div>
              <img src="../../../assets/images/solution/detail/download.png" alt="" @click.stop="downloadBtn(item)"
                style="cursor: pointer" />
            </div>
          </li>
        </ul>
      </div>
      <view-list :id="viewId" :type="viewType"></view-list>
      <img class="top" src="../../../assets/images/solution/detail/toTap.png" alt="" @click="scrollUp" />
      <div class="bottomEmpty"></div>
    </div>
  </div>
  <a-modal v-model:visible="showDownloadModal" title="提示" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="550px">
    <promptBox @downloadModalCancel="downloadModalCancel" @downloadModalConfirm="downloadModalConfirm" />
  </a-modal>
  <!--<a-modal @downloadFormCancel="downloadFormCancel" @downloadFormConfirm="downloadFormConfirm">
    <reviewForm />
  </a-modal>-->
  <a-modal v-model:visible="showDownloadForm" title="新增工单" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="600px">
    <reviewForm @downloadFormCancel="downloadFormCancel" @downloadFormConfirm="downloadFormConfirm" />
  </a-modal>
  <!-- <a-modal
    v-model:visible="showDispatchModal"
    @ok="handleDispatchConfirm"
    @cancel="handleDispatchCancel"
    :maskClosable="false"
    :footer="null"
    width="700px"
    class="dispatch-modal"
  >
  	<template #title>
      <div class="custom-modal-title">
        <img
          src="@/assets/apply_icon.png"
          alt="调度类型"
          class="title-icon"
          style="width: 14px;margin-right: 10px;"
        />
        <span style="color: #24456A;">选择调度工单类型</span>
      </div>
    </template>
    <div class="dispatch-type-container">
      <div
        class="dispatch-type-item"
        :class="{ active: selectedDispatchType === '售前调度' }"
        @click="handleDispatchSelect('售前调度')"
      >
        <img
          :src="selectedDispatchType === '售前调度' ? selectedLeftIcon : selectLeftIcon"
          alt="售前调度"
        />
        <div class="dispatch-type-text">售前调度</div>
      </div>
      <div
        class="dispatch-type-item"
        :class="{ active: selectedDispatchType === '售中调度' }"
        @click="handleDispatchSelect('售中调度')"
      >
        <img
          :src="selectedDispatchType === '售中调度' ? selectedCenterIcon : selectCenterIcon"
          alt="售中调度"
        />
        <div class="dispatch-type-text">售中调度</div>
      </div>
      <div
        class="dispatch-type-item"
        :class="{ active: selectedDispatchType === '售后调度' }"
        @click="handleDispatchSelect('售后调度')"
      >
        <img
          :src="selectedDispatchType === '售后调度' ? selectedRightIcon : selectRightIcon"
          alt="售后调度"
        />
        <div class="dispatch-type-text">售后调度</div>
      </div>
    </div>
    <div class="dispatch-action">
      <a-button @click="handleDispatchCancel">取消</a-button>
      <a-button type="primary" @click="handleDispatchConfirm">确定</a-button>
    </div>
  </a-modal> -->
</template>
<script setup>
import { onMounted, ref, reactive } from "vue";
import { isShowToolTip } from "../../../utils/index.js";
import { useRouter, useRoute } from "vue-router";
import formField from "./formField.js";
import {
  getDetail,
  getDownCount,
  cancelCollect,
  collect,
  getNewDownCount,
} from "../../../api/solutionNew/detail";
import { pptTopdf } from "../../../api/fileUpload/uploadFile.js";
import { getEcologicalDetails } from "@/api/login/login.js";
import viewList from "../../product/detail/viewList.vue";
import { getTradeList } from "../../../api/solutionNew/home";
import { getAllUserList, roleDetail } from "@/api/system/user.js";
import {
  addShoppingCart,
  toCombinePage,
} from "../../../api/combine/shoppingCart.js";
import axios from "axios";
import { message } from "ant-design-vue";
import eventBus from "../../../utils/eventBus";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";

// 添加图片导入
// const selectLeftIcon = new URL(
//   "../../../assets/images/Coordination/select_left.png.png",
//   import.meta.url
// ).href;
// const selectedLeftIcon = new URL(
//   "../../../assets/images/Coordination/selected_left.png",
//   import.meta.url
// ).href;
// const selectCenterIcon = new URL(
//   "../../../assets/images/Coordination/select_center.png",
//   import.meta.url
// ).href;
// const selectedCenterIcon = new URL(
//   "../../../assets/images/Coordination/selected_center.png",
//   import.meta.url
// ).href;
// const selectRightIcon = new URL(
//   "../../../assets/images/Coordination/select_right.png",
//   import.meta.url
// ).href;
// const selectedRightIcon = new URL(
//   "../../../assets/images/Coordination/selected_right.png",
//   import.meta.url
// ).href;

const route = useRoute();
const viewId = ref(route.query.id);
const viewType = ref("1");
const loadShow = ref(false);
const showDownloadModal = ref(false);
const showDownloadForm = ref(false);
// const showDispatchModal = ref(false);
const selectedDispatchType = ref("");
const roleKey = JSON.parse(localStorage.getItem("userInfo")).roleKeyList;
const videoData = reactive({
  style: "position: fixed;right:200%;",
});
onMounted(() => {
  getData();
  setTimeout(() => {
    const videoDom = document.getElementById("video");
    if (videoDom.videoHeight > videoDom.videoWidth) {
      videoData.style = "height:400px;";
    } else {
      videoData.style = "width: 100%;";
    }
  }, 2000);
});
const functionKey = ref(1);
const isActive = ref(0);
const collectActive = ref(false);
const videoRef = ref(null);
const ecoName = ref(null);
const getTabs = JSON.parse(localStorage.getItem("userInfo"));

function change(v) {
  isActive.value = v;
}
const downloadBtn = (e) => {
  console.log("下载超限");
  //loadShow.value = true;
  getNewDownCount({
    businessId: route.query.id,
    businessType: 1,
  })
    .then((res) => {
      if (res.code == 200) {
        if (res.data) {
          const href = e.url;
          const downName = e.name;
          let windowOrigin = window.location.origin;
          let token = localStorage.getItem("token");
          let newHref = href;
          if (href.includes(windowOrigin)) {
            newHref = "/portal" + href.split(windowOrigin)[1];
          }
          window.open(windowOrigin + newHref + "?token=" + token);
          return false;
        } else {
          showDownloadModal.value = true;
        }
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

const ecologyDeal = (value) => {
  if (value) {
    return value;
  }
  return "-";
};

const addAI = () => {
  let addParams = {
    schemeId: route.query.id,
    type: "1",
  };
  localStorage.removeItem("seekName");
  localStorage.setItem("AInowCustomizeSolutionId", route.query.id);
  toCombinePage({
    conclusion: "谢谢聆听",
    cover: detailData.name,
    list: [addParams],
    source: "2",
  }).then((res) => {
    Router.push({
      query: {
        type: 2,
      },
      name: "newAllProject",
    });
  });
};

const shelfTimeWith = (value) => {
  if (value) {
    return value.slice(0, 10);
  }
  return "-";
};
const dealData = (value) => {
  if (value) {
    return value;
  } else {
    return "-";
  }
};
const fileShow = (val) => {
  console.log("下载超限");
  // 转PDF的下载
  loadShow.value = true;
  pptTopdf({
    filePath: val,
  }).then((res) => {
    loadShow.value = false;
    if (res.code == 200) {
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = res.data;
      if (res.data.includes(windowOrigin)) {
        newHref = "/portal" + res.data.split(windowOrigin)[1];
      }
      const newpage = Router.resolve({
        name: "lookPdf",
        query: {
          urlMsg: encodeURIComponent(
            windowOrigin + newHref + "?token=" + token
          ),
          urlName: val.name,
        },
      });
      window.open(newpage.href, "_blank");
    }
  });
  return false;
};
const userInfo = JSON.parse(localStorage.getItem("userInfo"));
const showApply = ref(false);
console.log("userInfo", userInfo);

const tabList = ref([]);
const getTarde = () => {
  let tradeParams = {};

  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
getTarde();

const toolTipShow = (item, num) => {
  let wordNum = "";
  item.map((val) => {
    wordNum += val.summary + "\n";
  });
  return wordNum && wordNum.length >= num;
};

const fileNameWith = (value) => {
  let createShow = false;
  let shelfTime = "";
  if (detailData.value.shelfTime) {
    shelfTime = detailData.value.shelfTime.slice(0, 10).replace(/-/g, "");
  }
  createShow = Number(shelfTime) < 20241226;
  let fileName = {
    1: createShow ? "政策背景" : "方案概述",
    2: "市场分析",
    3: "需求分析",
    4: "方案概述",
    5: "应用场景",
    6: "部署方案",
    7: "合作模式",
    8: "应用案例",
  };
  return fileName[value.type];
};
const collectById = () => {
  if (detailData.value.collect == 1) {
    cancelCollect(route.query.id)
      .then(() => {
        message.success("取消收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    collect(route.query.id)
      .then(() => {
        message.success("收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  }
  collectActive.value = !collectActive.value;
};
const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const toDetail = (value) => {
  Router.push({
    query: {
      id: value.id,
    },
    name: "applyNew",
  });
};
const currentAnchor = ref("#desc");
const back = () => {
  Router.back();
  return false;
  if (route.query.type == "scheme") {
    Router.push({
      name: "lowLevel",
      query: {
        activeNum: "4",
        zoneId: route.query.zoneId,
      },
    });
  } else {
    Router.push({
      name: "topContentNew",
      query: {
        activeNum: "1",
      },
    });
  }
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
const detailData = ref({});
const valueData = ref([]);
const add = () => {
  addShoppingCart({
    schemeId: route.query.id,
    type: "1",
  }).then((res) => {
    getData();
    eventBus.emit("cartRefresh");
  });
};
const apply = () => {
  // showDispatchModal.value = true;
  handleDispatchSelect("售前调度");
  handleDispatchConfirm();
};
const handleDispatchSelect = (type) => {
  if (
    !userInfo.roleKeyList.includes("deliveryManager") &&
    type === "售中调度"
  ) {
    message.warning("目前售中调度工单仅支持交付经理发起");
    return;
  } else if (type === "售后调度") {
    return message.warning("售后调度工单暂未开放");
  } else {
    selectedDispatchType.value = type;
  }
};
const handleDispatchConfirm = () => {
  if (!selectedDispatchType.value) {
    message.warning("请选择调度工单类型");
    return;
  }

  if (selectedDispatchType.value === "售前调度") {
    const params = {
      id: route.query.id,
      action: "edit",
      to: "starWork",
      active: '调度中心',
    };
    const searchParams = new URLSearchParams(params);
    window.location.replace(
      window.location.origin + "/#/dispatchCenter/transfer?" + searchParams.toString()
    );
  } else if (selectedDispatchType.value === "售中调度") {
    if (!userInfo.roleKeyList.includes("deliveryManager")) {
      message.warning("目前售中调度工单仅支持交付经理发起");
      return;
    }
    const params = {
      id: route.query.id,
      action: "edit",
      to: "coordination",
      active: '调度中心',
    };
    const searchParams = new URLSearchParams(params);
    window.location.replace(
      window.location.origin + "/#/dispatchCenter/transfer?" + searchParams.toString()
    );
  } else if (selectedDispatchType.value === "售后调度") {
    message.warning("售后调度工单流程暂未开放，敬请期待！");
    return;
  }
  // showDispatchModal.value = false;
};
// const handleDispatchCancel = () => {
//   selectedDispatchType.value = "";
//   showDispatchModal.value = false;
// };
const anchorPoints = ref([
  {
    key: "bac",
    href: "#bac",
    title: "方案概述",
    type: 1,
  },
  {
    key: "apply",
    href: "#apply",
    title: "应用场景",
    type: 5,
  },
  {
    key: "example",
    href: "#example",
    title: "应用案例",
    type: 8,
  },
]);

const transformEcoPartnerList = (originalList) => {
  // 边界情况处理
  if (!originalList || !Array.isArray(originalList)) {
    return [];
  }

  // 使用Map实现高性能分组
  const groupMap = originalList.reduce((map, item) => {
    // 跳过无效元素
    if (!item || typeof item !== "object") return map;

    try {
      // 安全提取字段（防止undefined报错）
      const {
        ecopartnerName,
        ecopartnerId,
        enterpriseId,
        totalScore,
        introScore,
        sync,
        auth,
        approve,
        delStatus,
      } = item;
      const key = `${ecopartnerName}|${ecopartnerId}|${enterpriseId}`;

      // 自动跳过缺失关键字段的元素
      if (!key.includes("undefined")) {
        if (!map.has(key)) {
          map.set(key, {
            ecopartnerName: ecopartnerId ? ecopartnerName : ecoName.value,
            ecopartnerId,
            enterpriseId,
            totalScore,
            introScore,
            sync,
            auth,
            type: ecopartnerId == null ? 1 : 2,
            approve,
            delStatus,
            children: [],
          });
        }
        // 安全提取子字段
        map.get(key).children.push({
          contactName: item.ecologyContact || item.contactName || "",
          contactPhone: item.ecologyPhone || item.contactPhone || "",
          contactAddress: item.contactAddress || "",
          ecologyContact: item.ecologyContact || item.contactName || "",
          ecologyPhone: item.ecologyPhone || item.contactPhone || "",
        });
      }
    } catch (e) {
      console.warn("Invalid data format:", e);
    }

    return map;
  }, new Map());

  return Array.from(groupMap.values());
};

const getData = () => {
  if (route.query.id) {
    console.log(userInfo.orgId, `userInfo.orgId`);
    console.log(userInfo.orgNamePath.split("/")[0], `userInfo.orgNamePath`)
    if (
      (userInfo.orgId === 2 ||
      userInfo.orgId === 3 ||
      userInfo.orgId === 4 ||
      userInfo.orgId === 5 ||
      userInfo.orgId === 6 ||
      userInfo.orgId === 7 ||
      userInfo.orgId === 8 ||
      userInfo.orgId === 9 ||
      userInfo.orgId === 11 ||
      userInfo.orgId === 13 ||
      userInfo.orgId === 14 ||
      (userInfo.orgId !== 173 &&
        userInfo.orgId !== 184 &&
        userInfo.orgId !== 185 &&
        userInfo.orgId !== 211 &&
        userInfo.orgId !== 212 &&
        userInfo.orgId !== 213 &&
        userInfo.orgId !== 174 &&
        userInfo.orgId !== 186 &&
        userInfo.orgId !== 190 &&
        userInfo.orgId !== 191 &&
        userInfo.orgId !== 192) ||
      userInfo.orgId === 10 ||
      userInfo.orgId == 12) && userInfo.orgNamePath.split("/")[0] === "江苏公司"
    ) {
      showApply.value = true;
    }
    getDetail(route.query.id).then(async (res) => {
      roleDetail(res.data.contactId).then(val=>{
		    if(val.data.orgNamePath && val.data.orgNamePath.split("/").length == 3) {
		      if( val.data.orgNamePath.split("/")[1] == "苏移集成"){
		        ecoName.value = "江苏移动信息系统集成有限公司";
		      } else {
		        ecoName.value = val.data.orgNamePath.split("/")[1]
		      }
		    } else {
		      ecoName.value = val.data.orgName
		    }
        anchorList.value = [];
        res.data.videoList = res.data.solutionFileList.filter(
          (item) => item.category == 6 || item.type == 5
        );
        res.data.videoList.forEach((item) => {
          item.url =
            window.location.origin +
            item.url +
            "?token=" +
            localStorage.getItem("token");
        });
        res.data.solutionFileList = res.data.solutionFileList.filter(
          (item) => item.category != 6 && item.type != 5
        );
        if (res.data.moduleBody && res.data.moduleBody.length > 1) {
          let indexType8 = res.data.moduleBody.findIndex(
            (item) => item.type == 8
          );
          if (indexType8 != -1) {
            res.data.moduleBody[indexType8].moduleList.forEach((item) => {
              item.image = item.image.split(",");
              item.fileWays = item.fileWays ? item.fileWays : "1";
              item.caseType = item.caseType ? item.caseType : 1;
              if (item.fileWays == "2") {
                item.pptUrl = item.fileUrl;
                item.pptName = item.fileName;
                item.pptPath = item.filePath;
                item.autoFile = true;
              }
            });
          }
          let indexType5 = res.data.moduleBody.findIndex(
            (item) => item.type == 5
          );
          if (indexType5 != -1) {
            res.data.moduleBody[indexType5].moduleList.forEach((item) => {
              item.sceneType = item.sceneType ? item.sceneType : 1;
            });
          }
        } else {
          if (res.data.isSensitive == 1) {
            res.data.moduleBody = [{ moduleList: [], type: 1 }];
          } else {
            res.data.moduleBody = [
              { moduleList: [], type: 1 },
              { moduleList: [], type: 5 },
              { moduleList: [], type: 8 },
            ];
          }
          res.data.moduleBody[0].moduleList.push({
            image: res.data.cover,
            summary: res.data.summary,
            fileName: res.data.summaryFileName,
            fileUrl: res.data.summaryFileUrl,
            filePath: res.data.summaryFilePath,
            type: 1,
          });
          if (res.data.caseInfoList && res.data.caseInfoList.length > 0) {
            res.data.caseInfoList.forEach((item, index) => {
              res.data.moduleBody[2].moduleList.push({
                projectName: item.caseName,
                scale: item.projectAmount,
                time: item.projectTime,
                projectIntroduction: item.constructionContent,
                image: item.imageUrl.split(",")[0],
                fileUrl: item.pptUrl || "",
                filePath: item.pptPath || "",
                fileName: item.pptName || "",
                type: 8,
                id: item.id,
              });
            });
          }
          if (res.data.sceneInfoList && res.data.sceneInfoList.length > 0) {
            res.data.sceneInfoList.forEach((item, index) => {
              res.data.moduleBody[1].moduleList.push({
                name: item.name,
                summary: item.summary,
                image: item.imageUrl,
                fileUrl: item.pptUrl || "",
                filePath: item.pptPath || "",
                fileName: item.pptName || "",
                functionList: item.functionList || [],
                caseList: item.caseList || [],
                type: 5,
                id: item.id,
              });
            });
          }
        }
        res.data.createTime = res.data.createTime.slice(0, 10);
        res.data.moduleBody.sort((a, b) => a.type - b.type);
        res.data.moduleBody.forEach((item) => {
          const anchor = anchorPoints.value.find(
            (a) => a.type === item.type && item.moduleList.length > 0
          );
          if (anchor) {
            anchorList.value.push(anchor);
          }
        });

        //if(res.data.videoList.length>0){
        //	anchorList.value.unshift({
        //  	key: "video",
        //  	href: "#video",
        //  	title: "方案视频",
        //  	type: 10,
        //  })
        //}
        if (
          (res.data.ecopartnerList && res.data.ecopartnerList.length > 0) ||
          (res.data.enterpriseList && res.data.enterpriseList.length > 0)
        ) {
          anchorList.value = [
            ...anchorList.value,
            {
              key: "ecopartner",
              href: "#ecopartner",
              title: "生态合作",
              type: 0,
            },
          ];
          //res.data.enterpriseList && res.data.enterpriseList.length > 0
          if (false) {
            // let dataIds = [];
            // res.data.enterpriseList.forEach((item) => {
            //   if (item.enterpriseId > 10000) {
            //     dataIds.push(item.enterpriseId);
            //   }
            // });
            // if (dataIds.length > 0) {
            //   let result = await getEcologicalDetails(dataIds);
            //   let ecopartnerNewList = [];
            //   if (result.data) {
            //     result.data.forEach((item) => {
            //       let children = [];
            //       item.basicContactInfos.forEach((itemChild) => {
            //         children.push({
            //           ecologyContact: itemChild.contactName,
            //           ecologyPhone: itemChild.contactPhone,
            //           contactAddress: itemChild.contactAddress,
            //         });
            //       });
            //       ecopartnerNewList.push({
            //         ecopartnerName: item.enterpriseName,
            //         ecopartnerId: item.id,
            //         children: children,
            //       });
            //     });
            //     res.data.moduleBody = [
            //       {
            //         type: 0,
            //         moduleList: ecopartnerNewList,
            //       },
            //       ...res.data.moduleBody,
            //     ];
            //   }
            // } else {
            //   if (res.data.ecopartnerList && res.data.ecopartnerList.length > 0) {
            //     res.data.moduleBody = [
            //       {
            //         type: 0,
            //         moduleList: transformEcoPartnerList(res.data.ecopartnerList),
            //       },
            //       ...res.data.moduleBody,
            //     ];
            //   }
            // }
          } else if (
            res.data.ecopartnerList &&
            res.data.ecopartnerList.length > 0
          ) {
            res.data.moduleBody = [
              {
                type: 0,
                moduleList: transformEcoPartnerList(res.data.ecopartnerList),
              },
              ...res.data.moduleBody,
            ];
          }
        }
        if (res.data.solutionFileList.length > 0) {
          anchorList.value.push({
            key: "download",
            href: "#download",
            title: "方案附件",
            type: 9,
          });
        } else {
          res.data.fileList = false;
        }

        res.data.labelName = res.data.labelName.split(",");
        // res.data.provider = res.data.provider.split("/")[1];
        detailData.value = res.data;
        res.data.moduleBody.forEach((element) => {
          if (element.type == 8 && element.moduleList.length > 0) {
            let text = element.moduleList[0].projectIntroduction;
            let caseInfo = {};
            let keywords = {
              name: "项目名称[：:](.*?)\\n",
              size: "规模[：:](.*?)\\n",
              time: "时间[：:](.*?)\\n",
              people: "联系人[：:](.*?)\\n",
              intro: "(案例介绍|项目介绍)[：:](.*)", // 匹配"案例介绍"或"项目介绍"后的内容
            };
            for (let key in keywords) {
              let regex = new RegExp(keywords[key], "s");
              let match = text.match(regex);
              if (match) {
                caseInfo[key] =
                  key === "intro"
                    ? (match[2] || "").trim()
                    : (match[1] || "").trim();
              } else {
                caseInfo[key] = "";
                // 如果您不想在找不到匹配时保留该属性，可以取消注释下一行
                // delete caseInfo[key];
              }
            }
            element.caseInfo = caseInfo;
          }
        });
        let markList = res.data.moduleBody.filter((item) => {
          return item.type == 2;
        });
        let needList = res.data.moduleBody.filter((item) => {
          return item.type == 3;
        });
        let descList = res.data.moduleBody.filter((item) => {
          return item.type == 4;
        });
        res.data.moduleBody.map((value) => {
          if (value.type == 1) {
            if (markList && markList.length > 0)
              value.moduleList = [
                ...value.moduleList,
                ...markList[0].moduleList,
              ];
            if (needList && needList.length > 0)
              value.moduleList = [
                ...value.moduleList,
                ...needList[0].moduleList,
              ];
            if (descList && descList.length > 0)
              value.moduleList = [
                ...value.moduleList,
                ...descList[0].moduleList,
              ];
          }
        });
        console.log(res.data.moduleBody, `oooo`);

        valueData.value = res.data.moduleBody;
      });
    });
  }
};

const isShow = ref("desc");
const anchorList = ref([]);
const Router = useRouter();
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  isShow.value = href;
};
eventBus.on("solutionDetailRefresh", getData);
// 下载超限提示弹窗取消按钮
const downloadModalCancel = () => {
  showDownloadModal.value = false;
};
// 下载超限提示弹窗确认按钮
const downloadModalConfirm = () => {
  showDownloadModal.value = false;
  showDownloadForm.value = true;
};
const downloadFormCancel = () => {
  showDownloadForm.value = false;
};
const downloadFormConfirm = () => {
  showDownloadForm.value = false;
};

const goHtml = (item) => {
  if (item.sync == 1 && item.auth == 1) {
    console.log(item.ecopartnerId);
    if (item.ecopartnerId > 10000) {
      window.open(
        "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.ecopartnerId +
          "&token=bhubh3333ugy",
        "_blank"
      );
    } else {
      window.open(
        "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.enterpriseId +
          "&token=bhubh3333ugy",
        "_blank"
      );
    }
  }
};

// 行业经理发起工单
const applyProgram = () => {
  window.location.replace(
    window.location.origin +
      "/backend/#/ticket/addTicket?programId=" +
      route.query.id +
      `&routeName=waitWork`
  );
};
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>

<style lang="scss">
.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}

.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: rgba(214, 228, 255, 0.6);
    border-radius: 2px 2px 2px 2px;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  :deep(.ant-tabs-nav-wrap) {
    margin-top: 16px;
    width: 236px !important;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    // width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d6886 !important;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}

.caseinfo {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.right_name {
  margin-bottom: 0;
}

:deep(.ant-tabs-nav-wrap) {
  margin-top: 16px;
  width: 236px !important;
  overflow-x: auto !important;
  height: 48px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #ffffff;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}

// .dispatch-modal {
//   :deep(.ant-modal-header) {
//     border-bottom: none;
//     padding: 16px 24px;
//   }

//   :deep(.ant-modal-body) {
//     padding: 0 24px 24px;
//   }

//   :deep(.ant-modal-title) {
//     font-size: 16px;
//     font-weight: 500;
//     color: #333333;
//   }
// }

// .dispatch-type-container {
//   display: flex;
//   justify-content: space-between;
//   padding: 32px 40px;
// }

// .dispatch-type-item {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   cursor: pointer;
//   padding: 16px 24px;
//   border-radius: 8px;
//   transition: all 0.3s;
//   width: 140px;

//   &:hover {
//     border-color: #236cff;
//   }

//   &.active {
//     border-color: #236cff;
//     background-color: rgba(35, 108, 255, 0.04);
//   }

//   img {
//     width: 48px;
//     height: 48px;
//     margin-bottom: 12px;
//   }

//   .dispatch-type-text {
//     font-size: 14px;
//     color: #333333;
//     line-height: 22px;
//   }
// }

// .dispatch-action {
//   display: flex;
//   justify-content: center;
//   gap: 16px;

//   .ant-btn {
//     padding: 5px 16px;
//     height: 32px;
//     border-radius: 4px;
//     min-width: 80px;

//     &-primary {
//       background-color: #236cff;
//     }
//   }
// }
// .dispatch-modal {
//   :deep(.ant-modal-content) {
//     border-radius: 8px;
//     padding: 20px;
//   }

//   :deep(.ant-modal-header) {
//     margin-bottom: 40px;
//     padding: 0;
//     border-bottom: none;

//     .ant-modal-title {
//       font-weight: 500;
//       font-size: 16px;
//       color: #000000;
//       line-height: 24px;
//     }
//   }

//   :deep(.ant-modal-close) {
//     top: 20px;
//     right: 20px;
//   }

//   .dispatch-type-container {
//     display: flex;
//     justify-content: space-between;
//     padding: 0 40px;
//     margin-bottom: 40px;

//     .dispatch-type-item {
//       display: flex;
//       flex-direction: column;
//       align-items: center;
//       cursor: pointer;
//       width: 140px;
//       height: 140px;
//       background: #ffffff;
//       // border: 1px solid #e5e6eb;
//       border-radius: 8px;
//       padding: 20px;
//       transition: all 0.3s;

//       // &:hover {
//         // border-color: #4086ff;
//         // background: #f5f8ff;
//       // }

//       &.active {
//         // border-color: #4086ff;
//         // background: #f5f8ff;

//         .dispatch-type-text {
//           color: #4086ff;
//         }
//       }

//       img {
//         width: 120px;
//         height: 120px;
//         margin-bottom: 12px;
//       }

//       .dispatch-type-text {
//         font-weight: 500;
//         font-size: 22px;
//         color: #24456a;
//         text-align: center;
//       }
//     }
//   }

//   .dispatch-action {
//     display: flex;
//     justify-content: center;
//     gap: 12px;
//     margin-top: 100px;
//     .ant-btn {
//       min-width: 80px;
//       height: 32px;
//       font-weight: 500;
//       border: none;
//       font-size: 16px;
//       color: #0c70eb;
//       line-height: 22px;
//       border-radius: 4px;
//       font-weight: 400;
//       font-size: 14px;
//       padding: 4px 16px;
//       background: rgba(12, 112, 235, 0.08);
//       border-radius: 4px 4px 4px 4px;
//       &.ant-btn-primary {
//         background: #4086ff;
//         border-color: #4086ff;
//         color: #ffffff;

//         &:hover {
//           background: #6aa1ff;
//           border-color: #6aa1ff;
//         }
//       }

//       &.ant-btn-default {
//         color: #4e5969;
//         // border-color: #e5e6eb;
//         background: #ffffff;

//         &:hover {
//           color: #4086ff;
//           border-color: #4086ff;
//           background: #ffffff;
//         }
//       }
//     }
//     .submit {
//       background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
//       border-radius: 4px 4px 4px 4px;
//       font-weight: 500;
//       font-size: 14px;
//       color: #ffffff;
//       line-height: 22px;
//     }
//   }
// }
</style>
