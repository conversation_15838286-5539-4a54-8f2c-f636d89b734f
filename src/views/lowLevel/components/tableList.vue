<template>
  <div class="searchInfo flex" :class="switchOnOff ? 'AIBg' : 'commonBg'">
    <div
      style="
        min-height: 56px;
        background: #ffffff;
        justify-content: center;
        align-items: center;
      "
      class="flex"
    ></div>

    <div class="vocationPull" style="flex: 1; height: 56px">
      <!-- <div class="switch">
        <div class="AIlogo"></div>
        <a-switch
          checked-children="on"
          un-checked-children="off"
          v-model:checked="switchOnOff"
          @change="switchAi"
        />
      </div> -->
      <a-input
        v-model:value="name"
        class="inputClass"
        allow-clear
        @keyup.enter="seekContent"
        placeholder="请输入产品名称、标签等关键词进行检索"
      />
      <!--<voiceRecorder
        v-if="switchOnOff"
        :isTranslating="isTranslating"
        :canBtnUse="canBtnUse"
        @audioReady="handleAudio"
      />-->
      <div
        class="seekInfo"
        :class="switchOnOff ? 'AIbtn' : 'commonBtn'"
        @click="seekContent"
      >
        <img src="@/assets/images/home/<USER>" />
        <div>搜索</div>
      </div>
    </div>
  </div>

  <div class="textWord" v-show="currentItem">政策洞察</div>
  <div class="readContent" v-if="currentItem">
    <div
      :class="[
        'animate__animated pointer',
        {
          animate__fadeInRight: rightAnimate,
          animate__fadeInLeft: leftAnimate,
        },
      ]"
      @click="goDetail(currentItem)"
    >
      <div class="text">{{ currentItem.name }}</div>
      <div class="date">
        发表日期：{{
          currentItem.publishTime
            ? timeDeal(currentItem.publishTime)
            : timeDeal(currentItem.createTime)
        }}
      </div>
      <div class="desc" v-html="currentItem.details"></div>
    </div>

    <div class="operation" v-if="commonPolicyList.length > 1">
      <a-button
        type="text"
        shape="circle"
        @click="showPrevious"
        :disabled="currentIndex == 0"
        style="
          width: 56px;
          height: 56px;
          background: linear-gradient(
            163deg,
            #f1f3f6 0%,
            #f6f7f9 38%,
            #ffffff 100%
          );
          box-shadow: 4px 4px 12px 0px rgba(0, 0, 0, 0.06),
            -2px -2px 8px 0px rgba(255, 255, 255, 0.5);
        "
      >
        <template #icon>
          <LeftOutlined
            style="color: rgba(12, 112, 235, 0.2); font-size: 20px"
          />
        </template>
      </a-button>

      <a-button
        type="text"
        shape="circle"
        @click="showNext"
        :disabled="currentIndex == commonPolicyList.length - 1"
        style="
          margin-left: 48px;
          width: 56px;
          height: 56px;
          background: linear-gradient(
            163deg,
            #f1f3f6 0%,
            #f6f7f9 38%,
            #ffffff 100%
          );
          box-shadow: 4px 4px 12px 0px rgba(0, 0, 0, 0.06),
            -2px -2px 8px 0px rgba(255, 255, 255, 0.5);
        "
      >
        <template #icon>
          <RightOutlined
            style="color: rgba(12, 112, 235, 0.2); font-size: 20px"
          />
        </template>
      </a-button>
    </div>
  </div>
  <!-- <policy-list v-show="policyShow" :policyParam="name" ref="policyRef" /> -->
  <div class="textWord" v-show="schemeShow">标准方案</div>
  <scheme-list v-show="schemeShow" :schemeParam="name" ref="schemeRef" />
  <div class="textWord" v-show="ablityShow">原子能力</div>
  <ablity-list v-show="ablityShow" :ablityParam="name" ref="ablityRef" />
  <div class="textWord" v-show="productShow">标准产品</div>
  <product-list v-show="productShow" :productParam="name" ref="productRef" />

  <div
    class="emptyPhoto"
    v-show="!policyShow && !schemeShow && !ablityShow && !productShow"
  >
    <img src="@/assets/images/home/<USER>" />
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  computed,
  watch,
  onUnmounted,
} from "vue";
import schemeList from "./scheme.vue";
import ablityList from "./ablity.vue";
import productList from "./product.vue";
import policyList from "./policy.vue";
import eventBus from "@/utils/eventBus";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
import { LeftOutlined, RightOutlined } from "@ant-design/icons-vue";
import { useRouter } from "vue-router";

export default defineComponent({
  components: {
    schemeList,
    ablityList,
    voiceRecorder,
    productList,
    policyList,
    LeftOutlined,
    RightOutlined,
  },
  name: "topContent",
  props: {
    policyInsightsList: Array,
    default() {
      return {};
    },
  },
  setup(props) {
    const Router = useRouter();
    const policyRef = ref(null);
    const schemeRef = ref(null);
    const ablityRef = ref(null);
    const productRef = ref(null);
    const data = reactive({
      name: "",
      switchOnOff: true,
      isTranslating: false,
      canBtnUse: false,
      rightAnimate: false,
      leftAnimate: false,
      currentIndex: 0,
      currentItem: {},
      commonPolicyList: [],
    });

    watch(
      () => props.policyInsightsList,
      (val) => {
        if (val) {
          data.commonPolicyList = val;
          data.currentItem = data.commonPolicyList[data.currentIndex];
        }
      },
      { immediate: true }
    );

    const policyShow = computed(() => {
      return policyRef.value && policyRef.value.totalItemCount > 0
        ? true
        : false;
    });
    const schemeShow = computed(() => {
      return schemeRef.value && schemeRef.value.totalItemCount > 0
        ? true
        : false;
    });
    const ablityShow = computed(() => {
      return ablityRef.value && ablityRef.value.totalItemCount > 0
        ? true
        : false;
    });
    const productShow = computed(() => {
      return productRef.value && productRef.value.totalItemCount > 0
        ? true
        : false;
    });
    const seekContent = () => {
      if (data.switchOnOff) {
        if (data.name) {
          eventBus.emit("getSchemeAIList");
          eventBus.emit("getAblityAIList");
          eventBus.emit("getProductAIList");
        }
      } else {
        eventBus.emit("getSchemeList");
        eventBus.emit("getPolicyList");
        eventBus.emit("getAblityList");
        eventBus.emit("productRefresh");
      }
    };

    onUnmounted(() => {
      eventBus.off("getSchemeAIList");
      eventBus.off("getAblityAIList");
      eventBus.off("getProductAIList");
      eventBus.off("getSchemeList");
      eventBus.off("getPolicyList");
      eventBus.off("getAblityList");
      eventBus.off("productRefresh");
    });

    const goDetail = (val) => {
      Router.push({ name: "visionDetail", query: { id: val.id } });
    };

    const timeDeal = (value) => {
      if (value) {
        return value.slice(0, 10).replaceAll("-", ".");
      }
      return "-";
    };

    const showNext = async () => {
      if (data.rightAnimate) return;
      data.rightAnimate = true;
      if (data.currentIndex < data.commonPolicyList.length - 1) {
        data.currentIndex++;
        data.currentItem = data.commonPolicyList[data.currentIndex];
      }
      await new Promise((resolve) => {
        setTimeout(() => {
          data.rightAnimate = false;
          resolve();
        }, 1000);
      });
    };

    const showPrevious = async () => {
      if (data.leftAnimate) return;
      data.leftAnimate = true;
      if (data.currentIndex > 0) {
        data.currentIndex--;
        data.currentItem = data.commonPolicyList[data.currentIndex];
      }
      await new Promise((resolve) => {
        setTimeout(() => {
          data.leftAnimate = false;
          resolve();
        }, 1000);
      });
    };

    const switchAi = (val) => {
      data.switchOnOff = val;
    };
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append("file", audioBlob, "recording.wav"); // 上传文件
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true;
      data.canBtnUse = true;
      AIvoice(formData).then((res) => {
        data.isTranslating = false;
        data.canBtnUse = false;
        if (res.code == 200) {
          data.name = res.msg;
        }
      });
    };
    return {
      ...toRefs(data),
      seekContent,
      switchAi,
      showPrevious,
      showNext,
      schemeRef,
      productRef,
      goDetail,
      ablityRef,
      handleAudio,
      policyRef,
      policyShow,
      timeDeal,
      ablityShow,
      productShow,
      schemeShow,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss" scoped>
</style>