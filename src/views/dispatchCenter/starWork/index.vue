<template>
  <div style="display: flex;justify-content: center;margin-top: 20px;">
    <div id="addAbilit" class="background_fff" style="width:1200px">
      <div>
        <!-- {{ action }} -->
        <div v-if="viewLoading" class="loading-overlay">
          <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
        </div>
        <div v-if="formLoading" class="loading-overlay">
          <a-spin :spinning="formLoading" tip="数据加载中..." />
        </div>
        <div v-if="action === 'edit'" class="form-title-class">发起售前调度支撑</div>
        <a-form ref="mainFormRef" :model="formData" labelAlign="right" :rules="rules"
          class="operation padding_l_24 padding_t_24">
          <p v-if="action !== 'edit'" class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>工单信息
          </p>
          <div v-if="action !== 'edit'">
            <a-row>
              <a-col :span="10">
                <a-form-item label="工单标题">
                  {{ formData.title }}
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <p class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>基础信息
          </p>
          <div class="base_info">
            <div>
              <a-row v-if="action == 'edit'">
                <a-col :span="10">
                  <a-form-item label="项目名称" name="projectName">
                    <a-input v-model:value="formData.projectName" placeholder="请输入DICT项目管理系统项目名称"
                      :maxlength="50"></a-input>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="!userInfo.roleKeyList.includes('ecologicalPartner') && action != 'edit'">
                <a-col :span="10">
                  <a-form-item label="项目名称">
                    {{ formData.projectName }}
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div>
              <a-row v-if="action == 'edit'">
                <a-col :span="10">
                  <a-form-item label="项目编码" name="projectCode">
                    <a-input v-model:value="formData.projectCode" type="number" placeholder="请输入DICT项目管理系统项目编码"
                      @input="limitLength" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="!userInfo.roleKeyList.includes('ecologicalPartner') && action != 'edit'">
                <a-col :span="10">
                  <a-form-item label="项目编码">
                    {{ formData.projectCode || '-' }}
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <a-row v-if="action == 'edit'">
              <a-col :span="10">
                <a-form-item label="需求发起人" name="userInfo">
                  <a-input v-model:value="formData.userInfo"></a-input>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-else>
              <a-col :span="10">
                <a-form-item label="需求发起人">
                  {{ formData.userInfo || '-' }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-if="action == 'edit'">
              <a-col :span="10">
                <a-form-item label="联系方式" name="userPhone">
                  <a-input disabled v-model:value="formData.userPhone"></a-input>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-else>
              <a-col :span="10">
                <a-form-item label="联系方式">
                  {{ formData.userPhone || '-' }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-if="action == 'edit'">
              <a-col :span="10">
                <a-form-item label="支撑方式" name="supportMehod">
                  <a-radio-group v-model:value="formData.supportMehod">
                    <a-radio value="1">远程支撑</a-radio>
                    <a-radio value="2">现场支撑</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-else>
              <a-col :span="10">
                <a-form-item label="支撑方式">
                  {{ formData.supportMehod == 1 ? '远程支撑' : formData.supportMehod == 2 ? '现场支撑' : '-' }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-if="action == 'edit'">
              <a-col :span="10">
                <a-form-item label="支撑时限" name="time">
                  <a-date-picker style="width: 60%" v-model:value="formData.time" format="YYYY-MM-DD"
                    :value-format="'YYYY-MM-DD'" :disabled-date="disabledDate" />
                  前
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-else>
              <a-col :span="10">
                <a-form-item label="计划支撑时限">
                  {{ formData.time || '-' }}前
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <!-- Work Order Flow Component -->
          <work-order-flow
            v-if="action != 'edit' || formData.moduleForm.length > 0"
            :action="action"
            :order-id="Route.query.orderId"
            :module-form="formData.moduleForm"
            :order-comments="orderComments"
            :table-data-work="tableDataWork"
            :form-loading="formLoading"
            :add-loading="addLoading"
            :is-created-by-myself="isCreatedByMyself"
            :support-type="support_type"
            :contral-support="contralSupport"
            :user-info="userInfo"
            @submit="submit"
            @reject="handleReject"
            @end-work="endWork"
            @re-submit="reSubmit"
            @add-module="addModule"
            @delete-module="handleDeleteModule"
            @add-cooperate="addCooperate"
            @check-change="onCheckChange"
            @select-module="handleSelectModule"
            @score-confirm="handleOkScore"
            @suggest-confirm="handleOkSugget"
            @reject-confirm="backLastComit"
          >
            <template #moduleSelection="{ moduleIndex }">
              <guide-table :sourceType="formData.moduleForm[moduleIndex]?.businessModuleType" @close="closeModal"
                @ok="(val) => handleSelectModule(val, moduleIndex)"></guide-table>
            </template>
          </work-order-flow>

          <!-- Work Order Flow Section -->
          <template v-if="action == 'edit'">
            <p class="group_title weight500 font_14 font_00060e">
              <span class="icon"></span>售前调度流程：
            </p>
          </template>
              <template
                v-if="moduleItem.textList && moduleItem.textList.length > 0 && action != 'edit' && moduleItem.status != 'selectApply' && moduleItem.status != 'toSelect' && moduleItem.status != 'reSelectPage'">
                <a-descriptions style="margin-top: 10px;" v-for="(textItem, index) in moduleItem.textList" :key="index"
                  bordered size="small" :column='2'>
                  <a-descriptions-item label="生态能力方" :span="2">{{ textItem &&
                    textItem.company
                  }}&nbsp;&nbsp;{{
                      textItem && textItem.contactUserName
                    }}&nbsp;&nbsp;{{ textItem && textItem.contactPhone
                    }}</a-descriptions-item>
                  <a-descriptions-item label="是否支撑">{{ textItem &&
                    textItem.dealType == '1' ? '同意' : textItem.dealType == '2' ? '拒绝' : ''
                  }}</a-descriptions-item>
                  <a-descriptions-item v-if="textItem && textItem.dealType == '1'" label="支撑人天">{{
                    textItem && textItem.allParseData.supportCos
                  }}</a-descriptions-item>
                  <a-descriptions-item v-if="textItem && textItem.dealType == '1'" label="实际支撑结果">{{
                    textItem && textItem.allParseData.suggest }}</a-descriptions-item>
                  <a-descriptions-item label="回复时间">{{ textItem
                    &&
                    textItem.dealTime }}</a-descriptions-item>
                  <a-descriptions-item v-if="textItem && textItem.dealType == '2'" label="拒绝原因">{{
                    textItem && textItem.dealContent || '-' }}</a-descriptions-item>
                  <a-descriptions-item
                    v-if="textItem && textItem.dealType == '1' && textItem.allParseData.fileListDeal.length != 0"
                    label="附件" :span="2">
                    <div class="file-list" v-if="textItem.allParseData.fileListDeal.length != 0">
                      <div class="flex">
                        <p>
                          <span>
                            <i class="iconfont icon-annex"></i>
                            <span>
                              &nbsp;{{ textItem.allParseData.fileListDeal[0]?.name }} &nbsp;</span>
                          </span>
                        </p>
                        <div class="font_0c70eb">
                          <span @click="view(textItem.allParseData.fileListDeal[0])">
                            &nbsp;预览</span>
                          <span @click="download(textItem.allParseData.fileListDeal[0])">
                            &nbsp;下载</span>
                        </div>
                      </div>
                    </div>
                  </a-descriptions-item>
                  <!-- enterpriseId为null是自有能力方或自有联系人 -->
                  <a-descriptions-item v-if="textItem.enterpriseId && textItem &&
                    textItem.dealType == '1'">
                    <template #label>
                      <div style=" display: flex; ">
                        <span>支撑满意度</span>
                        <el-tooltip effect="dark" content="请对售前支撑整体满意度打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <div class="header-help-icon">
                            <el-icon :size="12">
                              <QuestionFilled />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </div>
                    </template>
                    <template v-if="textItem && textItem.dealType == '1'">
                      <el-input-number
                        v-if="moduleItem.status == 'writeScore' && (textItem && textItem.dealType == '1' && !textItem.scored)"
                        v-model="textItem.satisfiedScore" :min="0.1" :max="10" :step="1">
                      </el-input-number>
                      <span v-else>{{ textItem && textItem.satisfiedScore }}</span>
                    </template>
                  </a-descriptions-item>
                  <!-- enterpriseId为null是自有能力方或自有联系人 -->
                  <a-descriptions-item v-if="textItem.enterpriseId && textItem && textItem.dealType == '1'">
                    <template #label>
                      <div style="display: flex; ">
                        <span>售前响应及时率</span>
                        <el-tooltip effect="dark" content="请对售前支撑响应及时率的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                          placement="top">
                          <div class="header-help-icon">
                            <el-icon :size="12">
                              <QuestionFilled />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </div>
                    </template>
                    <template v-if="textItem && textItem.dealType == '1'">
                      <el-input-number
                        v-if="moduleItem.status == 'writeScore' && textItem && textItem.dealType == '1' && !textItem.scored"
                        v-model="textItem.responseScore" :min="0.1" :max="10" :step="1">
                      </el-input-number>
                      <span v-else>{{
                        textItem
                        &&
                        textItem.responseScore
                      }}</span>
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item v-if="textItem && textItem.dealType == '1'" label="生态评价" :span="2">
                    <el-input
                      v-if="moduleItem.status == 'writeScore' && textItem && textItem.dealType == '1' && !textItem.scored"
                      v-model="textItem.suggest" type="textarea" placeholder="请输入生态评价" :rows="4" :cols="50"
                      :maxlength="100" :show-word-limit="true"></el-input>
                    <span v-else>{{
                      textItem &&
                      textItem.comment
                    }}</span>
                  </a-descriptions-item>
                </a-descriptions>
              </template>
              <div
                v-if="action != 'dealedAll' && (moduleItem.status == 'reSelectPage' || moduleItem.status == 'reStar') && (!moduleItem.needProvince && moduleItem.isBySelf == '2' && (moduleItem.textList.some(text => text.dealType == '1' || text.dealType == '2')))">
                <el-table :data="dataCompanyNew" class="resize-table-header-line" :empty-text="'暂无数据'"
                  :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px; margin-bottom: 20px">
                  <el-table-column v-if="moduleItem.ecologyType.includes('2')" prop="company" label="生态厂商">
                    <template #default="scope">
                      <div class="table-row-wrapper" style="overflow-x: auto;">
                        <div v-for="(companyItem, index) in scope.row.company" :key="index" class="box"
                          style="width: max-content; min-width: 100%; text-align: left; display: flex; align-items: center; gap: 5px; margin-bottom: 26px; white-space: nowrap;">
                          <a-radio-group :value="moduleItem.selectId" style="display: inline-block; ">
                            <a-radio :value="companyItem.contactPhone"
                              @change="(e) => onCheckChange(e, companyItem, moduleIndex, '0')"
                              :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)">
                            </a-radio>
                          </a-radio-group>
                          <span v-if="companyItem.ecopartnerName" class="company_left company_underline"
                            style="display: inline-block; " @click="toCompanyDetail(companyItem)">
                            {{ companyItem.ecopartnerName }}
                          </span>
                          <div class="company_right" style="display: inline-flex; align-items: center; ">
                            <img width="20px" height="20px" style="margin-bottom: 0;" src="@/assets/images/score.png" />
                            <span>生态评分：<span style="color: #FF9C39FF;font-weight: bold;">{{ companyItem.totalScore ||
                              '-' }}</span></span>
                          </div>
                          <a-select v-model:value="companyItem.contactName"
                            @change="(value) => selectUserCom(value, companyItem)"
                            :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1) || rejectCompanyIdlist.some((value) => value.userId == companyItem.userId)">
                            <template v-for="(opt, index) in companyItem.contactList" :key="index">
                              <a-select-option :value="opt.contactName"
                                :disabled="opt.approve != 1 || rejectCompanyIdlist.some((value) => value.userId == opt.userId)">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span
                            :style="{ color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999' }">
                            {{ companyItem.contactPhone }}
                          </span>
                          <span
                            v-if="companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve && companyItem.approve != 1"
                            style="color: red; margin-left: 12px">生态厂商暂无该生态联系人！</span>
                          <span v-else-if="companyItem.auth == 0"
                            style="color: red; margin-left: 12px">该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！</span>
                          <span v-else-if="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve)"
                            style="color: red; margin-left: 12px">生态厂商暂无该生态联系人！</span>
                        </div>
                        <a-button class="custom_btn active_btn margin_top_10"
                          @click="addCooperate(scope.row, moduleIndex)">新增生态厂商</a-button>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="moduleItem.ecologyType.includes('1') && dataCompanyNew?.[0]?.ownPerson?.length > 0"
                    prop="ownPerson" label="自有能力方">
                    <template #default="scope">
                      <div v-for="(ownPersonItem, index) in scope.row.ownPerson" :key="index" class="box"
                        style="display: flex; margin-bottom: 16px">
                        <a-radio-group :value="moduleItem.selectIdOwn">
                          <a-radio :value="ownPersonItem.belong"
                            @change="(e) => onCheckChange(e, ownPersonItem, moduleIndex, '1')"
                            :disabled="rejectCompanyIdlist.some((value) => value.userId == ownPersonItem.userId)">
                          </a-radio>
                        </a-radio-group>
                        <div style="width: 100%;text-align: left;display: flex;align-items: center;">
                          <span style="display: flex; align-items: center">
                            <span v-if="ownPersonItem.contactList" style="display: flex; align-items: center">
                              <a-select v-model:value="ownPersonItem.contactName"
                                @change="(value) => selectUserCom(value, ownPersonItem)">
                                <template v-for="(opt, optIndex) in ownPersonItem.contactList" :key="optIndex">
                                  <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                                    {{ opt.contactName }}
                                  </a-select-option>
                                </template>
                              </a-select>
                              <span style="margin-right: 8px;display: inline-block;margin-left: 8px;">
                                {{ ownPersonItem.contactPhone }}
                              </span>
                            </span>
                            <span v-else>
                              <p class="contactName">
                                {{ ownPersonItem.contactName }}
                              </p>
                              <p class="contactPhone">
                                {{ ownPersonItem.contactPhone }}
                              </p>
                            </span>
                            {{ ownPersonItem.belong }}
                          </span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- reStar（重新发起），重新发起不显示自有联系人 -->
                  <el-table-column
                    v-if="moduleItem.status != 'reStar' && support_type == '省级' && !contralSupport && (moduleItem.ecopartnerList && moduleItem.ecopartnerList[0] && moduleItem.ecopartnerList[0].ownProvince && moduleItem.ecopartnerList[0].ownProvince.length > 0)"
                    prop="ownProvince" label="自有联系人">
                    <template #default="scope">
                      <div v-for="(ownProvinceItem, index) in scope.row.ownProvince" :key="index" class="box"
                        style="display: flex; margin-bottom: 16px">
                        <a-radio-group :value="moduleItem.selectIdOwn">
                          <a-radio :value="ownProvinceItem.belong"
                            @change="(e) => onCheckChange(e, ownProvinceItem, moduleIndex, '2')"
                            :disabled="rejectCompanyIdlist.some((value) => value.userId == ownProvinceItem.userId)">
                          </a-radio>
                        </a-radio-group>
                        <div style="width: 100%;text-align: left;display: flex;align-items: center;">
                          <span style="display: flex; align-items: center">
                            <span v-if="ownProvinceItem.contactList" style="display: flex; align-items: center">
                              <a-select v-model:value="ownProvinceItem.contactName"
                                @change="(value) => selectUserCom(value, ownProvinceItem)">
                                <template v-for="(opt, index) in ownProvinceItem.contactList" :key="index">
                                  <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                                    {{ opt.contactName }}
                                  </a-select-option>
                                </template>
                              </a-select>
                              <span style="margin-right: 8px;display: inline-block;margin-left: 8px;">
                                {{ ownProvinceItem.contactPhone }}
                              </span>
                            </span>
                            <span v-else>
                              <p class="contactName">
                                {{ ownProvinceItem.contactName }}
                              </p>
                              <p class="contactPhone">
                                {{ ownProvinceItem.contactPhone }}
                              </p>
                            </span>
                            {{ ownProvinceItem.belong }}
                          </span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="scrollable-table-wrap">
                <el-table v-if="moduleItem.status == 'selectApply'" :data="moduleItem.tableData1" :empty-text="'暂无数据'"
                  :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
                  style="width: 100%; margin-top: 20px;" class="scrollable-table resize-table-header-line">
                  <el-table-column prop="companyData" label="自有能力方/生态厂商" width="580">
                    <template #default="scope">
                      <span style="margin-right: 12px; display: inline-block">{{ scope.row.companyData.ecopartnerName
                        }}</span>
                      <span style="margin-right: 12px; display: inline-block">{{ scope.row.companyData.contactName
                        }}</span>
                      <span>{{ scope.row.companyData.contactPhone }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否支撑">
                    <template #default>
                      <el-button link type="primary" size="small" htmlType="button"
                        @click="submitCompany">同意</el-button>
                      <el-button link type="danger" size="small" htmlType="button"
                        @click="refuseApplyCompany">拒绝</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <p v-if="action != 'edit'" class="group_title weight500 font_14 font_00060e" style="margin-top:20px">
                模块流程节点：
                <span style="font-weight:normal;">{{ moduleItem.nodeName }}</span>
              </p>
              <p v-if="action != 'dealedAll' && moduleItem.status == 'reStar'"
                class="group_title weight500 font_14 font_00060e" style="margin-top:20px">驳回理由：
                <span style="font-weight:normal;color:red;">{{ moduleItem.auditReason }}</span>
              </p>
              <div v-if="moduleItem.status != 'selectApply' && action != 'dealedAll'" class="flex just-center"
                style="margin-top:10px;">
                <a-button v-if="moduleItem.status == 'toSelect' || moduleItem.status == 'reSelectPage'"
                  class="margin_r_10 custom_btn reject_btn" @click="handleReject(moduleIndex)">驳回</a-button>
                <!-- <a-button v-if="moduleItem.status == 'submitPage'" class="custom_btn active_btn"
                  @click="submitBtn(moduleIndex)" :loading="addLoading">调度完成</a-button> -->
                <a-button v-if="action != 'edit' && moduleItem.status != 'reStar' && moduleItem.status != 'submitPage'"
                  class="custom_btn active_btn" @click="submit(moduleIndex)" :loading="addLoading">确定</a-button>
                <a-button v-if="(moduleItem.status == 'reStar' || moduleItem.status == 'submitPage')"
                  class="margin_r_10 custom_btn cancel_btn" @click="endWork(moduleIndex)">结束</a-button>
                <a-button v-if="moduleItem.status == 'reStar'" class="custom_btn active_btn"
                  @click="reSubmit(moduleIndex)" :loading="addLoading">重新提交</a-button>
              </div>
            </div>
          </div>
            </div>
          </template>
          <div v-if="action == 'edit'" style="width: 100%;text-align: center;margin: 20px 0 0 0 ;">
            <a-button class="custom_btn active_btn" @click="addModule">新增支撑模块</a-button>
          </div>
          <a-row v-if="(action == 'edit' || hasReStar) && action != 'dealedAll'"
            style="padding-left: 42px;margin-top:20px">
            <a-col :span="10">
              <a-form-item label="调度人" name="dispatchUser">
                <a-select placeholder="请选择调度人" v-model:value="formData.dispatchUser" allowClear>
                  <template v-for="opt in personList" :key="opt.id">
                    <a-select-option :value="String(opt.id)">
                      {{ formatRealName(opt.realName, opt.remark) }}
                    </a-select-option>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <p v-if="action != 'edit' && isCreatedByMyself" class="group_title weight500 font_14 font_00060e margin_t_24">
            <span class="icon"></span>流程备忘录
          </p>
          <flow-memo v-if="action != 'edit' && isCreatedByMyself" :order-id="Route.query.orderId"
            v-model:order-comments="orderComments"></flow-memo>
          <!-- Flow memo and work order flow table are now handled by WorkOrderFlow component -->
        </a-form>
      </div>
      <div class="flex just-center margin_t_32 margin_b_24">
        <a-button v-if="action !== 'submitPage'" class="margin_r_10 custom_btn cancel_btn" @click="cancel">取消</a-button>
        <a-button v-if="action == 'edit'" class="custom_btn active_btn" @click="createOrder"
          :loading="addLoading">确定</a-button>
      </div>
    </div>
    <a-modal v-model:visible="showAdd" title="生态厂商新增" :footer="null" @close="closeAdd" width="50%">
      <a-form ref="addFormRef" :model="CooperateData" labelAlign="right" :rules="addRules">
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态合作方" name="company">
              <a-select placeholder="请选择生态合作方" v-model:value="CooperateData.company" allowClear show-search
                @select="ecologyChangeOld" @search="handleSearch" :not-found-content="fetching ? undefined : null"
                :filter-option="false" :virtual-scroll="{
                  itemHeight: 32,
                  height: 400,
                  remain: 8,
                }">
                <template v-for="opt in displayOptions" :key="opt.name">
                  <a-select-option :value="opt.name"
                    :disabled="formData.moduleForm[currentModuleIndex]?.companyIdList.includes(opt.enterpriseId)">
                    {{ opt.name }}
                  </a-select-option>
                </template>
              </a-select>
              <div v-if="CooperateData.company">
                <p v-if="CooperateData.sync != 1 || CooperateData.auth != 1"
                  style="color: red; margin-bottom: 0; margin-left: 12px">
                  该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                </p>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系人" name="contanct">
              <a-select placeholder="请选择生态联系人" v-model:value="CooperateData.contanct" allowClear
                @change="(value) => selectUser(value)">
                <template v-for="(opt, index) in contanctList" :key="index">
                  <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                    {{ opt.contactName }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系方式：">
              <a-input disabled :value="CooperateData.phone" placeholder="请输入生态联系方式">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="负责区域：">
              <a-input disabled :value="CooperateData.area" placeholder="请选择负责区域">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center margin_t_32">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="closeAdd">取消</a-button>
        <a-button class="custom_btn active_btn" @click="submitAdd" :loading="addLoading"
          :disabled="isSyncAuth">提交</a-button>
      </div>
    </a-modal>
    <a-modal v-model:visible="showSuggest" title="支撑反馈" @ok="handleOkSugget" :footer="null" @close="handleCloseSugget"
      width="50%">
      <a-form ref="dealFormRef" :model="formDataDeal" labelAlign="right" :rules="dealRules" class="operation">
        <a-form-item v-if="!refuseOpen && !isThirdEco" label="支撑人/天数：" name="supportCos">
          <a-input v-model:value="formDataDeal.supportCos" placeholder="请填写支撑人/天数"></a-input>
        </a-form-item>
        <a-form-item :label="refuseOpen ? '拒绝原因：' : '实际支撑结果：'" name="suggest">
          <a-textarea :rows="7" :showCount="true" :maxlength="refuseOpen ? 100 : 500"
            :placeholder="'请输入' + (refuseOpen ? '拒绝原因，限制100个字符' : '实际支撑结果内容，限制500个字符')"
            v-model:value="formDataDeal.suggest"></a-textarea>
        </a-form-item>
        <a-form-item v-if="!refuseOpen" label="附件：">
          <file-upload :customClassName="'custom_btn active_btn'" :fileInfo="{
            type: 'other',
            fileList: formDataDeal.fileListDeal,
            index: null,
            subIndex: null,
            accept: '.doc,.docx,.ppt,.pptx',
            required: false,
            category: '2',
            multiple: true,
            acceptLength: 1,
            size: 150,
            mark: '仅支持ppt,doc,docx格式的文件上传(文件个数限1个,单个文件限150M）',
          }" @update-load="viewFileData"></file-upload>
        </a-form-item>
      </a-form>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleCloseSugget"> 取消 </a-button>
        <a-button class="custom_btn active_btn" @click="handleOkSugget"> 确认 </a-button>
      </div>
    </a-modal>
    <a-modal v-model:visible="showScore" title="提示" @ok="handleOkScore" :footer="null" width="40%">
      <a-form ref="scoreFormRef" :model="formDataScore" labelAlign="right" :rules="rulesScore">
        <a-row>
          <a-col :span="24">
            <a-form-item label="是否转售中" name="isTurn">
              <a-radio-group v-model:value="formDataScore.isTurn" @change="changeRadio">
                <a-radio value="1">是</a-radio>
                <a-radio value="2">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="项目名称" name="projectName">
              <a-input v-model:value="formDataScore.projectName" placeholder="请填写DICT项目管理系统项目名称"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="项目编码" name="projectCode">
              <a-input type="number" v-model:value="formDataScore.projectCode"
                placeholder="请填写DICT项目管理系统项目编码"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="交付经理" name="deliveryManager">
              <a-select placeholder="请选择交付经理" v-model:value="formDataScore.deliveryManager" allowClear show-search
                :filter-option="filterOption">
                <template v-for="(opt, index) in personListNew" :key="index">
                  <a-select-option :value="String(opt.id)" :label="opt.realName">
                    {{ opt.realName }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div style="display: flex; padding-left: 65px; color: #a2abb5">
        <p>备注：</p>
        <p>选择是将发送短信给交付经理</p>
      </div>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleCloseScore"> 取消 </a-button>
        <a-button class="custom_btn active_btn" @click="handleOkScore"> 确认 </a-button>
      </div>
    </a-modal>
    <a-modal v-model:visible="showSuggestCity" title="处理意见" @ok="backLastComit" :footer="null"
      @close="handleCloseSuggeOld" width="50%">
      <a-textarea :rows="7" :showCount="true" :maxlength="500" placeholder="请输入处理意见，限制500个字符"
        v-model:value="suggest"></a-textarea>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleCloseSuggeOld"> 取消 </a-button>
        <a-button class="custom_btn active_btn" @click="backLastComit"> 确认 </a-button>
      </div>
    </a-modal>
    <a-modal :visible="moduleTypeVisible" @cancel="closeModal"
      :title="'选择' + businessModuleTypeComputed(formData.moduleForm[currentModuleIndex]?.businessModuleType)"
      width="1200px" centered :destroyOnClose="true" :maskClosable="false" :footer="null">
      <guide-table :sourceType="formData.moduleForm[currentModuleIndex]?.businessModuleType" @close="closeModal"
        @ok="(val) => handleSelectModule(val, currentModuleIndex)"></guide-table>
    </a-modal>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import fileUpload from "@/components/fileUpload/fileUpload.vue";
import GuideTable from "./components/guideTable.vue";
import FlowMemo from "./components/FlowMemo.vue";
import WorkOrderFlow from "@/components/WorkOrderFlow/WorkOrderFlow.vue";
import { submitWithdrawProcess, batchSubmitWithdrawProcess, cityBack, midDispatch } from "@/api/processManage/backlog&completed/index.js";
import { getPriviligesData } from "@/api/processManage/permissionConfig/index.js";
import { QuestionFilled } from "@element-plus/icons-vue"; // 添加引入QuestionFilled图标
import {
  getProcessInfo,
  isReselect,
  getProcessInfoNew,
  submitOrder,
  selectOrderById,
  operateOrderById,
  querytOrderById,
  selectOrderComments,
} from "@/api/processManage/index.js";
import { selectTree, selectTreeNew } from "@/api/system/team";
import {
  solutionDetail,
  sceneDetail,
  getPerson,
  beginSend,
  abilityDetail,
} from "@/api/ticket/ticket.js";
import { getAllUserList } from "@/api/system/user";
import { formatDateTime, groupDataByMonthWithStats, sortTextList, processDuplicateCompaniesByName } from '@/utils/index.js';
export default defineComponent({
  components: { fileUpload, QuestionFilled, GuideTable, FlowMemo, WorkOrderFlow },
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const getUserInfo = JSON.parse(window.localStorage.getItem("userInfo"));
    const data = reactive({
      hasReStar: false,
      isCreatedByMyself: false,
      moduleTypeVisible: false,
      currentModuleIndex: 0,
      operateIndex: 0, // 待操作的模块索引
      title: "",
      orderComments: [],
      showScore: false,
      showSuggestCity: false,
      isSubmit: false,
      // businessModuleType: "1", // 默认选择解决方案
      formData: {
        projectName: "",
        title: "", // 工单标题
        projectCode: "",
        dispatchUser: undefined,
        userPhone: "",
        userInfo: "",
        supportMehod: "",
        time: "",
        moduleForm: [{
          uid: null,
          status: 'edit',
          procInstId: "",
          taskId: "",
          // editFinishData: [], // 对生态厂商的评分
          // reSelectPage: false,// 是否重新选择厂商
          textList: [], // 生态厂商列表
          ownProvince: [], //自有联系人list
          tableData1: [],
          projectContent: "",
          fileList: [],
          businessModuleType: "1",
          name: "",
          intro: "",
          selectUsers: [],
          ecologyType: "",
          editDataCompany: [], // 模块可选择生态方列表
          reSelectData: [], // 厂商拒绝后调度人页面重新选择厂商数据
          ecopartnerList: [],
          selectCompanyList: {},
          companyIdList: [], //生态合作方中auth并sync的的公司id的list
          ipartnerId: "",
          selectId: "",
          selectIdOwn: "",
          selectPhone: "",
          // reDispatch: false, // true重新调度
          needProvince: false, // 是否申请省级调度支撑
          isBySelf: "2", // 1自己支撑2不自己支撑
          provinceUser: "", // 省级调度员id
        }]
      },
      personListNew: [],
      formDataScore: {
        isTurn: "2",
        projectName: "",
        projectCode: "",
        deliveryManager: "",
      },
      formDataDeal: {
        suggest: "",
        supportCos: "",
        fileListDeal: [],
      },
      refuseOpen: false,
      // selectIdOwn: undefined,
      support_type: "",
      // isFirst: true,
      contralSupport: false, //true省直调度人
      userInfo: getUserInfo,
      // selectCompanyId: undefined,
      ecopartnerId: undefined,
      CooperateData: {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        area: undefined,
        enterpriseId: undefined,
        projectCode: undefined,
      },
      isSyncAuth: false,
      enterpriseId: undefined,
      dispatchProvinceUser: "", // 省直调度员id
      formName: "",
      // isOwnModule: false,
      ownPersonPhone: [],
      contanctList: [],
      baseData: {
        name: "",
        intro: "",
      },
      teamOldList: [],
      definitionId: "",
      action: Route.query.action,
      // needCreateUsers: [],
      personList: [],
      provincePersonList: [],
      addLoading: false,
      rules: {
        title: [{ required: true, message: "请输入工单标题", trigger: "blur" }],
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        // projectCode: [
        //   { required: false, message: "项目编码不能为空" },
        //   { pattern: /[^0-9.]/g, message: "项目编码必须为纯数字" },
        // ],
        userInfo: [
          { required: true, message: "请输入需求发起人", trigger: "blur" },
        ],
        userPhone: [
          { required: true, message: "请输入需求发起人", trigger: "blur" },
        ],
        time: [{ required: true, message: "请选择支撑时限", trigger: "change" }],
        supportMehod: [
          { required: true, message: "请选择支撑方式", trigger: "change" },
        ],
        dispatchUser: [
          { required: true, message: "请选择调度人", trigger: "change" },
        ],
      },
      addRules: {
        company: [
          {
            required: true,
            message: "请选择生态合作方",
            trigger: "change",
          },
        ],
        contanct: [
          {
            required: true,
            message: "请选择联系人",
            trigger: "change",
          },
        ],
      },
      viewLoading: false,
      formLoading: false,
      dealRules: {
        suggest: [
          {
            required: true,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ],
        supportCos: [
          {
            required: true,
            message: "请输入支撑人/天数",
            trigger: "change",
          },
        ],
      },
      rulesScore: {
        isTurn: [
          {
            required: true,
            message: "请选择是否流转",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        projectCode: [{ required: true, message: "项目编码不能为空" }],
        deliveryManager: [
          {
            required: true,
            message: "请选择交付负责人",
            trigger: "change",
          },
        ],
      },
      // tableData1: [],
      tableDataWork: [],
      showAdd: false,
      dataCompanyNew: [],
      companyId: "",
      comId: undefined,
      // linkId: "", //方案或场景或能力id
      contentType: "", //1方案2场景3能力
      // dispatchUser: "", //调度人id
      isProvinceUser: false, //是否省级用户
      suggest: "", //处理意见
      showSuggest: false, //是否显示处理意见弹框
      isThirdEco: false, // 是否生态三方厂商
      companyAload: true,
      // rejectCompanyId: "", //拒绝支撑公司id
      // module: "",
      rejectCompanyIdlist: [], //厂商拒绝后调度人页面重新选择厂商数据
      reSelectCompany: {}, //厂商拒绝后调度人页面重新选择厂商数据
      // selectPhone: "", //工单发起页面选择人
      // selectCompanyId: "", //工单发起页面选择公司
      cityPass: false, //市级通过省级退回市级
    });
    const mainFormRef = ref(null);
    const addFormRef = ref(null);
    const dealFormRef = ref(null);
    const scoreFormRef = ref(null);
    const displayOptions = ref([]);
    const selectedValues = ref([]);
    const fetching = ref(false);
    const businessModuleTypeComputed = (type) => {
      if (type == 1) return '方案';
      if (type == 2) return '场景方案';
      if (type == 3) return '能力';
      return '';
    };
    const createOrder = async () => {
      // 发起售前调度申请
      await mainFormRef.value?.validate();
      data.addLoading = true;
      if (data.formData.projectCode) {
        if (data.formData.projectCode.includes("-")) {
          message.warning("项目编号应为纯数字");
          data.addLoading = false;
          return;
        }
      }
      for (let index = 0; index < data.formData.moduleForm.length; index++) {
        const element = data.formData.moduleForm[index];
        if (!element.name) {
          data.addLoading = false;
          message.warning("请选择业务模块" + (index + 1));
          return
        }
      }
      const moduleList = data.formData.moduleForm.filter(item => (item.businessModuleType && item.businessModuleType == '1'))
      if (moduleList.length > 1) {
        message.warning("请最多只选择一个方案");
        data.addLoading = false;
        return;
      }
      for (let index = 0; index < data.formData.moduleForm.length; index++) {
        const element = data.formData.moduleForm[index];
        if (!element.needProvince && (!element.selectIdOwn && !element.selectPhone)) {
          message.warning("请选择生态厂商或自有联系人");
          data.addLoading = false;
          return
        }
        if (element.needProvince && !element.provinceUser) {
          message.warning("请选择省级调度人");
          data.addLoading = false;
          return
        }
      }
      const temp = JSON.parse(JSON.stringify(data.formData));
      const { moduleForm, ...newObj } = temp;
      const postData = data.formData.moduleForm.map(module => {
        const { ecopartnerList, editDataCompany, ...moduleFormWithoutEcopartner } = module;// 移除moduleForm的ecopartnerList, editDataCompany
        return {
          ...newObj,
          ...moduleFormWithoutEcopartner,
          ...data.baseData,
          company: module.editDataCompany?.[0]?.company || [], // 生态厂商list
          ownPerson: module.editDataCompany?.[0]?.ownPerson || [], // 自有能力方list
          ownProvince: module.ownProvince || [], // 自有联系人list
          name: module.name, // 模块名称
          intro: module.intro, // 模块介绍
          companyIdList: module.companyIdList, // 生态合作方中auth并sync的的公司id的list
          dispatchUser: data.formData.dispatchUser, // 调度人id
          type: 8,
          time: data.formData.time, // 支撑时限
          contentType: module.businessModuleType, // 模块类型
          linkId: module.uid, // 模块id
          businessType: 11, // 调度支撑
          isProvince: data.isProvinceUser, // 是否省级用户
          workTitle: data.formData.title, // 工单标题，关于xxx的售前支撑工单
          supportType: data.support_type, // 省级或市级
          selectPhone: module.selectPhone, // 自有能力方或生态厂商联系方式
          selectIdOwn: module.selectIdOwn, // 自有能力方联系方式
          ipartnerId: module.ipartnerId, // 合作方用户id
          selectCompanyList: module.selectCompanyList, // 合作方企业信息
          needProvince: false, // 是否申请省级调度支撑
          isBySelf: "2", // 1自己支撑2不自己支撑
        }
      });
      submitOrder({
        customDataForm: JSON.stringify(postData),
        type: 8,
        projectName: data.formData.projectName,
        projectCode: data.formData.projectCode,
        businessType: 11, // 调度支撑
        title: data.formData.title,
        nextUserId: data.formData.dispatchUser, // 调度人id
        isProvince: data.isProvinceUser, // 是否省级用户
        comment: "工单派发给调度人",
        procDefId: data.definitionId,
      }).then((res) => {
        message.success("提交成功");
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const submit = async (index) => {
      data.operateIndex = index;
      const operateModule = data.formData.moduleForm[data.operateIndex];
      try {
        data.addLoading = true;
        if (operateModule.status == "writeScore") {
          // 评分
          handleWriteScore(operateModule);
        } else if (operateModule.status == "submitPage") {
          handleSubmitPage(operateModule);
        } else if (operateModule.status === "reSelectPage") {
          // 重新选择能力方（市级重新调度/省直重新调度/省级重新调度）
          handleReSelectPage(operateModule);
        } else if (operateModule.status == "toSelect") {
          if (data.contralSupport) {
            // 省直调度
            handleProvincialSupport(operateModule);
          } else {
            // 市级/省级调度人调度
            // 检查是否所有模块都已选择生态厂商或自有联系人
            handleCityAndProvince(operateModule);
          }
        } else {
          data.addLoading = false;
          console.log('other status')
        }
      } catch (error) {
        console.log(error);
        data.addLoading = false;
      };
    };
    const handleCityAndProvince = (operateModule) => {
      if (!operateModule.selectIdOwn && !operateModule.selectPhone) {
        message.warning("请选择生态厂商或自有联系人");
        data.addLoading = false;
        return;
      }
      let postData = []
      if (operateModule.needProvince) {
        // 需要省级支撑（市级调度）
        postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "申请省级调度支撑",
          nextUserIds: operateModule.provinceUser,
          variables: {
            needHelp: true,
            cityReject: false,
          }
        }];
      } else {
        // 不需要省级支撑（省级调度或市级调度）
        let companyNew = {
          module: operateModule.name,
          company: operateModule.selectCompanyList.ecopartnerName || operateModule.selectCompanyList.belong,
          contactPhone: operateModule.selectCompanyList.contactPhone,
          contactUserName: operateModule.selectCompanyList.contactName,
          contactUserJob: "",
          userId: operateModule.selectCompanyList.userId,
          enterpriseId: operateModule.selectCompanyList.enterpriseId,
        };
        if (data.support_type === "市级") {
          // 市级调度
          postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: "选择能力方",
            variables: {
              stfk: operateModule.ipartnerId || undefined,
              chooseType: "1",
              addCompanyInfo: JSON.stringify(companyNew),
              sendMsg: true,
              needHelp: false,
              cityReject: false,
              needProvinceSupport: false
            }
          }];
        } else {
          // 省级调度
          if (operateModule.isBySelf == "1") {
            // 自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "调度人自己支撑",
              variables: {
                chooseType: "2",
                selfSupport: true,
              },
              chooseType: "2",
            }];
          } else {
            // 不自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "选择能力方",
              variables: {
                stfk: operateModule.ipartnerId || undefined,
                chooseType: "1",
                addCompanyInfo: JSON.stringify(companyNew),
                sendMsg: true,
              }
            }];
          }
        }
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const handleProvincialSupport = (operateModule) => {
      let postData = []
      if (!operateModule.needProvince) {
        // 不需要省级支撑
        let companyNew = {
          module: operateModule.name,
          company: operateModule.selectCompanyList.ecopartnerName || operateModule.selectCompanyList.belong,
          contactPhone: operateModule.selectCompanyList.contactPhone,
          contactUserName: operateModule.selectCompanyList.contactName,
          contactUserJob: "",
          userId: operateModule.selectCompanyList.userId,
          enterpriseId: operateModule.selectCompanyList.enterpriseId,
        };
        postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "选择能力方",
          variables: {
            addCompanyInfo: JSON.stringify(companyNew),
            stfk: operateModule.selectCompanyList.userId,
            sendMsg: true,
            needProvinceSupport: false,
            chooseType: "1",
            feedbackReject: "3",
          },
        }];
      } else {
        // 需要省级支撑
        if (!operateModule.provinceUser) {
          message.warning("请选择省级调度人");
          data.addLoading = false;
          return
        }
        postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "申请省级调度支撑",
          nextUserIds: operateModule.provinceUser,
          variables: {
            needProvinceSupport: true,
            chooseType: "1",
          },
        }];
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const handlExistTask = () => {
      operateOrderById(Route.query.orderId).then((res) => {
        let tempProInstList = res.data.procInstList;
        if (tempProInstList && tempProInstList.length > 0) {
          // 该工单仍有待办，操作完成
          actionInitData();
        } else {
          window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
        }
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const actionInitData = () => {
      data.formData.moduleForm = [{
        uid: null,
        status: 'edit',
        procInstId: "",
        // editFinishData: [], // 对生态厂商的评分
        textList: [], // 生态厂商列表
        ownProvince: [], //自有联系人list
        tableData1: [],
        projectContent: "",
        fileList: [],
        businessModuleType: "1",
        name: "",
        intro: "",
        selectUsers: [],
        ecologyType: "",
        editDataCompany: [],
        reSelectData: [],
        ecopartnerList: [],
        selectCompanyList: {},
        ipartnerId: "",
        selectId: "",
        selectIdOwn: "",
        selectPhone: "",
        needProvince: false, // 是否申请省级调度支撑
        isBySelf: "2", // 1自己支撑2不自己支撑
      }];
      getData();
    }
    const handleReSelectPage = (operateModule) => {
      let company = {
        company: data.reSelectCompany.ecopartnerName || data.reSelectCompany.belong,
        contactPhone: data.reSelectCompany.contactPhone,
        contactUserName: data.reSelectCompany.contactName,
        contactUserJob: "",
        userId: data.reSelectCompany.userId,
        enterpriseId: data.reSelectCompany.enterpriseId,
      };
      // 重新调度
      if (!operateModule.needProvince && !company.userId && operateModule.isBySelf == '2') {
        message.warning("请选择生态厂商或自有联系人");
        throw new Error("请选择生态厂商或自有联系人");
      }
      if (operateModule.needProvince && !operateModule.provinceUser) {
        message.warning("请选择省级调度人");
        throw new Error("请选择省级调度人");
      }
      if (data.rejectCompanyIdlist.some((value) => value.userId == company.userId)) {
        message.warning("该厂商联系人已拒绝，请重新选择");
        throw new Error("该厂商联系人已拒绝，请重新选择");
      }
      let postData = [];
      if (data.support_type === "市级") {
        // 市级重新调度
        if (operateModule.needProvince) {
          // 申请省级支撑
          postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: "申请省级调度支撑",
            nextUserIds: operateModule.provinceUser,
            variables: {
              needHelp: true,
              cityReject: false
            }
          }];
        } else {
          // 不申请省级支撑
          postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: "重新选择能力方",
            variables: {
              addCompanyInfo: JSON.stringify({ ...company, module: operateModule.name }),
              stfk: company.userId,
              chooseType: "1",
              sendMsg: true,
              needHelp: false,
              cityReject: false,
              needProvinceSupport: false
            }
          }];
        }
      } else {
        // 省级/省直重新调度
        if (data.contralSupport) {
          if (operateModule.needProvince) {
            // 需要升级支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "申请省级调度支撑",
              nextUserIds: operateModule.provinceUser,
              variables: {
                needProvinceSupport: true,
                chooseType: "1",
              },
            }];
          } else {
            // 不需要省级支撑，省直重新调度
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "重新选择能力方",
              variables: {
                addCompanyInfo: JSON.stringify(company),
                stfk: company.userId,
                chooseType: "1",
                sendMsg: true,
                needProvinceSupport: false,
              },
            }];
          }
        } else {
          // 省级重新调度
          if (operateModule.isBySelf === "1") {
            // 自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "调度人自己支撑",
              variables: {
                chooseType: "2",
                selfSupport: true
              },
              chooseType: "2"
            }];
          } else {
            // 不自己支撑
            postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "重新选择能力方",
              variables: {
                addCompanyInfo: JSON.stringify({ ...company, module: operateModule.name }),
                stfk: company.userId,
                chooseType: "1",
                sendMsg: true,
              }
            }];
          }
        }
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const handleSubmitPage = (operateModule) => {
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: data.suggest,
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        data.suggest = "";
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const handleWriteScore = (operateModule) => {
      let tempTextList = operateModule.textList.filter((i) => i.enterpriseId);
      tempTextList = tempTextList.filter((i) => i.dealType && i.dealType == "1");// 同意的才可以评价
      const hasEmptyScore = tempTextList.some((i) => !i.scored && (!i.satisfiedScore || !i.responseScore));// 没有评过分，评分为空
      if (hasEmptyScore) {
        // 自有能力方、自有联系人不支持生态评分，生态厂商需要评分
        message.warning("评分不能为空");
        data.addLoading = false;
        return false;
      }
      // }
      querytOrderById(Route.query.orderId).then((res) => {
        const taskList = res.data || [];
        if (taskList.length > 1) {
          // 不是最后一个打分的模块，只评分
          // 只提交同意的且没有评过分的生态方数据
          handleOrderTask();
        } else {
          // 最后一个打分的模块，弹售中弹窗
          if (taskList.length == 1 && taskList[0].taskId == operateModule.taskId) {
            data.formDataScore.projectCode = data.formData.projectCode;
            data.formDataScore.projectName = data.formData.projectName;
            data.formDataScore.deliveryManager = undefined;
            data.formDataScore.isTurn = "2";
            data.showScore = true;
            data.addLoading = false;
          } else {
            handleOrderTask();
          }
        }
      });
    };
    const handleOrderTask = () => {
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const ownPerson = operateModule.textList.filter((item) => !item.enterpriseId && (!item.scored && item.dealType == '1')); // enterpriseId为null是自有联系人或自有能力方，不需要打分，需要生态评价
      const thirdPerson = operateModule.textList.filter((item) => item.enterpriseId && (!item.scored && item.dealType == '1')); // enterpriseId非null是生态厂商，需要打分，需要生态评价
      // 移除allParseData
      const filteredOwnPerson = ownPerson.map(({ allParseData, ...rest }) => rest);
      const filteredThirdPerson = thirdPerson.map(({ allParseData, ...rest }) => rest);
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: "评分完毕",
        variables: {
          addScoreInfo: JSON.stringify({ writeScore: ownPerson.length > 0 ? filteredOwnPerson : filteredThirdPerson }),
        },
      }];
      if (postData.length == 0) {
        message.warning("请至少选择一个生态厂商进行评分");
        data.addLoading = false;
        return;
      }
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success("评分成功");
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      });
    };
    const cancel = () => {
      window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      data.addLoading = false;
    };
    // 查询方案详情
    const querySolutionDetail = (id) => {
      solutionDetail(id).then(async (res) => {
        const tempModuleName = res.data.name;
        data.baseData.name = tempModuleName;
        data.baseData.intro = res.data.description;
        data.formName = "方案";
        data.formData.title = "关于" + tempModuleName + "的售前支撑工单";
        data.formData.moduleForm[data.currentModuleIndex].businessModuleType = '1'
        data.formData.moduleForm[data.currentModuleIndex].name = tempModuleName
        data.formData.moduleForm[data.currentModuleIndex].intro = res.data.summary
        // data.linkId = res.data.id;
        data.contentType = 1;
        formatEcopartnerList(data.currentModuleIndex, res.data);
      });
    }
    // 查询能力详情
    const querySchemeDetail = (id) => {
      abilityDetail(id).then(async (res) => {
        const tempModuleName = res.data.name;
        data.baseData.name = tempModuleName;
        data.baseData.intro = res.data.abilityIntro;
        data.formName = "能力";
        data.formData.title = "关于" + tempModuleName + "的售前支撑工单";
        data.formData.moduleForm[data.currentModuleIndex].businessModuleType = '3'
        data.formData.moduleForm[data.currentModuleIndex].name = tempModuleName
        data.formData.moduleForm[data.currentModuleIndex].intro = res.data.abilityIntro
        // data.linkId = res.data.id;
        data.contentType = 3;
        formatEcopartnerList(data.currentModuleIndex, res.data);
      });
    };
    // 查询场景详情
    const querySceneDetail = (id) => {
      sceneDetail(id).then(async (res) => {
        const tempModuleName = res.data.name;
        data.baseData.name = tempModuleName;
        data.baseData.intro = res.data.summary;
        data.formName = "场景";
        data.formData.title = "关于" + tempModuleName + "的售前支撑工单";
        data.formData.moduleForm[data.currentModuleIndex].businessModuleType = '2'
        data.formData.moduleForm[data.currentModuleIndex].name = tempModuleName
        data.formData.moduleForm[data.currentModuleIndex].intro = res.data.summary
        // data.linkId = res.data.id;
        data.contentType = 2;
        formatEcopartnerList(data.currentModuleIndex, res.data);
      });
    };
    // 格式化生态合作方list
    const formatEcopartnerList = async (index, { ecopartnerList, ecologyType, contact, createBy, phone, provider, name }) => {
      ecopartnerList = ecopartnerList || [];
      // 设置生态类型
      data.formData.moduleForm[index].ecologyType = ecologyType;
      let tempCompany = []; // 自有能力方
      let tempOwnPerson = []; // 生态厂商
      // 根据生态类型过滤数据
      if (ecologyType) {
        if (ecologyType.includes("2")) {
          // 生态厂商
          tempCompany = ecopartnerList.filter(item => item.ecopartnerId !== null);
        }
        if (ecologyType.includes("1")) {
          // 自有能力方
          tempOwnPerson = ecopartnerList.filter(item => item.ecopartnerId == null);
          // 批量处理自有能力方信息
          const promises = tempOwnPerson.map(async (person) => {
            if (person.contactPhone) {
              person.belong = await handleEcopartnerInfo(person.contactPhone);
            }
          });
          await Promise.all(promises);
        }
      }
      // 设置生态伙伴列表（只包含有ecopartnerId的项）
      data.formData.moduleForm[index].ecopartnerList = tempCompany;
      // 筛选授权且同步的企业ID
      data.formData.moduleForm[index].companyIdList = data.formData.moduleForm[index].ecopartnerList.filter(item => item.auth === 1 && item.sync === 1).map(item => item.enterpriseId);
      // 批量处理联系人列表 - 使用Promise.all优化异步处理
      const contactPromises = data.formData.moduleForm[index].ecopartnerList
        .filter(item => item.ecopartnerName) // 只处理有名称的项
        .map(async (item) => {
          try {
            const res = await selectTreeNew(item.ecopartnerName);
            item.contactList = res.data?.[0]?.contactList || [];
          } catch (error) {
            console.error(`获取联系人列表失败: ${item.ecopartnerName}`, error);
            item.contactList = [];
          }
        });
      // 等待所有联系人信息获取完成
      await Promise.all(contactPromises);
      // 解析provider信息
      const providerParts = provider ? provider.split("/") : [];
      const belongDepartment = providerParts[2] || '';
      // 构建模块自有联系人信息
      data.formData.moduleForm[index].ownProvince = [{
        contactName: contact, // 模块联系人
        userId: createBy, // 模块创建人id
        contactPhone: phone, // 模块联系方式
        belong: belongDepartment, // 模块归属部门
      }];
      // 构建售前调度流程表格数据
      data.formData.moduleForm[index].editDataCompany = [{
        name: name, // 模块名称
        company: tempCompany, // 生态厂商
        ownPerson: tempOwnPerson, // 自有能力方
      }];
    };
    const getData = () => {
      // if (Route.query.type == "submitPage") {
      //   // 调度人确认
      //   data.action = "submitPage";
      // }
      // if (Route.query.type == "writeScore") {
      //   data.action = "writeScore";
      // }
      // if (Route.query.type == "dealedAll") {
      //   data.action = "dealedAll";
      // }
      data.formData.userPhone = data.userInfo.phone;// 发起人联系方式
      data.formData.userInfo = data.userInfo.realName.charAt(0) + "经理";// 需求发起人
      let params = {
        pageNum: 1,
        pageSize: 10,
        processName: "售前调度工单",
      };
      beginSend(params).then((res) => {
        // 获取售前调度工单流程id
        data.definitionId = res.data ? res.data.rows[0].definitionId : "";
      });
      if (
        Route.query.type == "reSelect" ||
        Route.query.type == "dealedAll" ||// 查看详情
        Route.query.type == "selectApply" || // 合作方处理同意\拒绝操作
        Route.query.type == "editDetailFinish" ||
        Route.query.type == "writeScore" || // 发起人评分
        Route.query.type == "toSelect" || // 省级调度人选择合作方
        Route.query.type == "editDetailStar" ||
        Route.query.type == "submitPage" || // 调度人确认
        Route.query.type == "toSelectFirst" || // 省直调度管理员选择合作方
        Route.query.type == "multiModule" // 多模块
      ) {
        data.formLoading = true;
        // if (Route.query.type == "writeScore") {
        getAllUserList({ roleId: 71, scope: 0 }).then((res) => {
          // 转售中时的交付经理列表
          data.personListNew = res.data;
        });
        // }
        const apiCall = Route.query.type === "dealedAll" ? selectOrderById : operateOrderById;
        apiCall(Route.query.orderId).then((res) => {
          data.formData = { ...data.formData, ...res.data };
          let tempProInstList = res.data.procInstList;
          // 支撑模块是否自有联系人/自有能力方
          // const isOwnEcosystemUser = tempProInstList.some(item => {
          //   const tempParsedData = JSON.parse(JSON.parse(item.customDataForm));
          //   const isOwnPerson = tempParsedData.ownPerson.some(person => data.userInfo.id == person.userId);
          //   const isOwnProvince = tempParsedData.ownProvince.some(person => data.userInfo.id == person.userId);
          //   return ['1', '2'].includes((isOwnPerson || isOwnProvince));
          // })
          // if (Route.query.type == "selectApply" || (Route.query.type == "dealedAll" && (data.userInfo.roleKeyList.includes('ecologicalPartner') || isOwnEcosystemUser))) {
          //   // 生态厂商，只渲染对应的支撑模块
          //   tempProInstList = tempProInstList.filter(item => item.preSaleDispatchFeedbackInfos && item.preSaleDispatchFeedbackInfos.some(info => info.userId == data.userInfo.id));
          // }
          data.isCreatedByMyself = res.data.createBy == data.userInfo.id; // 是否自己创建的工单
          if (data.action != 'edit' && data.isCreatedByMyself) {
            // 如果是自己创建的工单，查询工单评论
            queryOrderComments(Route.query.orderId);
          }
          if (tempProInstList && tempProInstList.length > 0) {
            const tempParsedData = JSON.parse(JSON.parse(tempProInstList[0].customDataForm));
            data.formData = { ...data.formData, ...tempParsedData }; // 填充工单信息和基础信息
            for (let index = 0; index < tempProInstList.length; index++) {
              // 遍历并回填模块及生态方信息
              const element = tempProInstList[index];
              const parsedData = JSON.parse(JSON.parse(element.customDataForm));
              if (!data.formData.moduleForm[index]) {
                data.formData.moduleForm.push({
                  uid: null,
                  status: 'edit',
                  procInstId: "",
                  // editFinishData: [], // 对生态厂商的评分
                  textList: [], // 生态厂商列表
                  ownProvince: [], //自有联系人list
                  tableData1: [],
                  projectContent: "",
                  fileList: [],
                  businessModuleType: "1",
                  name: "",
                  intro: "",
                  selectUsers: [],
                  ecologyType: "",
                  editDataCompany: [],
                  reSelectData: [],
                  ecopartnerList: [],
                  selectCompanyList: {},
                  ipartnerId: "",
                  selectId: "",
                  selectIdOwn: "",
                  selectPhone: "",
                  needProvince: false, // 是否申请省级调度支撑
                  isBySelf: "2", // 1自己支撑2不自己支撑
                });
              }
              data.dispatchProvinceUser = parsedData.dispatchProvinceUser;
              data.formData.time = dayjs(parsedData.time).format('YYYY年MM月DD日'); // 支撑时限
              const { ownPerson = [], ownProvince = [] } = parsedData || {};
              const [personPhone, provincePhone] = [ownPerson[0]?.contactPhone, ownProvince[0]?.contactPhone];
              data.ownPersonPhone = [personPhone, provincePhone].filter(Boolean);
              // data.isOwnModule = data.ownPersonPhone.includes(data.userInfo?.phone);
              // data.formData.moduleForm[index].selectId = element.selectId;
              data.cityPass = parsedData.cityPass;// 省级才返回该值
              // data.formData.moduleForm[index].selectIdOwn = element.selectIdOwn;
              // data.rejectCompanyId = parsedData.rejectCompany;
              data.formData.moduleForm[index] = { ...data.formData.moduleForm[index], ...parsedData };
              if (Route.query.type == "multiModule") {
                const taskName = element?.tasks?.[0]?.taskName;
                if (taskName == "调度中" || taskName == "地市调度人" || taskName == "省级调度人") {
                  data.formData.moduleForm[index].status = "toSelect";
                }
                if (taskName == "省直调度管理员") {
                  data.formData.moduleForm[index].status = "toSelectFirst";
                }
                if (taskName == "生态评分") {
                  data.formData.moduleForm[index].status = "writeScore";
                }
                if (taskName == "调度人确认") {
                  data.formData.moduleForm[index].status = "submitPage";
                }
                if (taskName == "提交申请") {
                  data.formData.moduleForm[index].status = "reStar";
                }
                if (taskName == "生态反馈" || taskName == "能力方反馈") {
                  data.formData.moduleForm[index].status = "selectApply";
                }
              } else if (Route.query.type == "dealedAll") {
                data.action = "dealedAll"
              } else {
                data.action = Route.query.type
              }
              data.formData.moduleForm[index].taskId = element?.tasks?.[0]?.taskId || element?.tasks?.[0]?.id || "";
              data.formData.moduleForm[index].procInstId = element.procInstId;
              data.formData.moduleForm[index].selectCompanyList = parsedData.selectCompanyList || {};
              data.reSelectCompany.userId = parsedData.selectCompanyList.userId; // 重新选择的用户id
              data.reSelectCompany.ecopartnerName = parsedData.selectCompanyList.ecopartnerName; // 重新选择的企业名
              data.reSelectCompany.contactPhone = parsedData.selectCompanyList.contactPhone; // 重新选择的联系方式
              data.reSelectCompany.contactName = parsedData.selectCompanyList.contactName; // 重新选择的联系人
              data.reSelectCompany.enterpriseId = parsedData.selectCompanyList.enterpriseId; // 重新选择的企业id
              data.formData.moduleForm[index].ecopartnerList = (parsedData.company?.length > 0 && parsedData.company[0].name) ? parsedData.company : [{
                name: parsedData.name,
                company: parsedData.company,
                ownPerson: parsedData.ownPerson || [],
                ownProvince: parsedData.ownProvince || [],
              }];
              if (parsedData.ownProvince) {
                data.formData.moduleForm[index].ownProvince = parsedData.ownProvince;
              }
              data.formData.moduleForm[index].projectContent = parsedData.projectContent;
              data.formData.moduleForm[index].fileList = parsedData.fileList || [];
              data.formData.moduleForm[index].nodeName = element.historyProcNodeList[0].activityName;
              if (data.formData.moduleForm[index].status == 'reStar') {
                actioAuditReason(element.historyProcNodeList, parsedData, index);
              }
              queryEcoparterComment(element?.preSaleDispatchFeedbackInfos, index, parsedData);
            }
            data.tableDataWork = res.data.historyProcNodeList.reverse();
            if (data.tableDataWork[data.tableDataWork.length - 1].activityName == "已审核") {
              data.tableDataWork = data.tableDataWork.slice(0, -1);
            }
            if (data.tableDataWork[data.tableDataWork.length - 1].activityName == "结束") {
              data.tableDataWork = data.tableDataWork.slice(0, -1);
            }
            data.tableDataWork.forEach((item) => {
              if (item.commentList && item.commentList.length > 0) {
                item.dealContent = item.commentList[0].fullMessage;
                if (item.activityName == "调度人确认" && item.commentList[0].message == "") {
                  item.dealContent = "请发起人评分";
                }
                if (item.activityName == "能力方反馈") {
                  if (item.commentList[0].type == "2") {
                    item.dealContent = "驳回。" + item.commentList[0].message;
                  }
                }
                if (item.activityName == "生态反馈" || item.activityName == "能力方反馈") {
                  if (item.commentList[0].type == 1) {
                    item.dealContent = "同意。" + item.commentList[0].message;
                  } else if (item.commentList[0].type == 2) {
                    item.dealContent = "拒绝。" + item.commentList[0].message;
                  }
                }
              } else {
                item.dealContent = "";
              }
              if (item.endTime == null) {
                item.endTime = "-";
              }
            });
          }
          data.hasReStar = data.formData.moduleForm.some(item => item.status == 'reStar')
        }).finally(() => {
          data.formLoading = false;
        });

        if (Route.query.type == "editDetailFinish") {
          data.action = "editDetailFinish";
        }
        if (Route.query.type == "editDetailStar") {
          data.action = "editDetailStar";
        }
        data.formData.moduleForm.map(item => {
          if (item.status == "toSelectFirst") {
            // 调度人选择合作方
            data.contralSupport = true; // 是省直调度人
            item.status = "toSelect";
            const existReject = data.formData.moduleForm[data.currentModuleIndex].textList.some((item) => item.dealType == "2");
            if (existReject) {
              // 有拒绝的反馈
              item.status = "reSelectPage";
            }
          }
        });
      } else if (Route.query.from == "scene") {
        // 场景申请支撑
        querySceneDetail(Route.query.id);
      } else if (Route.query.from == "ability") {
        // 能力申请支撑
        querySchemeDetail(Route.query.id);
      } else if (Route.query.from == "center") {
        // 从调度中心工作台发起的申请支撑
      } else {
        // 方案申请支撑
        querySolutionDetail(Route.query.id);
      }
      getPersonList();
    };
    const actioAuditReason = (nodeList, parsedData, index) => {
      let tableDataWork = JSON.parse(JSON.stringify(nodeList));
      if (tableDataWork[tableDataWork.length - 1].activityName === "已审核") {
        tableDataWork = tableDataWork.slice(1, -1);
      } else {
        tableDataWork = tableDataWork.slice(1);
      }
      if (tableDataWork[tableDataWork.length - 1].activityName === "结束") {
        tableDataWork = tableDataWork.slice(0, -1);
      }
      const distDataList = tableDataWork
        .reverse()
        .slice(1)
        .map((item) => {
          const result = {
            activityName: item.activityName,
            assigneeName: item.assigneeName,
            phone: item.phone,
            dealContent: "",
            endTime: item.endTime || "-",
          };
          if (item.commentList?.length > 0) {
            const comment = item.commentList[0];
            result.dealContent = comment.fullMessage;
            if (item.activityName == "能力方反馈") {
              if (item.commentList[0].type == "2") {
                item.dealContent = "驳回。" + item.commentList[0].message;
              }
            }
          }
          data.selectId = parsedData.selectPhone;
          data.selectIdOwn = parsedData.selectIdOwn;
          return result;
        });
      data.formData.moduleForm[index].auditReason = distDataList[distDataList.length - 1]?.dealContent;
    };
    const queryOrderComments = (orderId) => {
      selectOrderComments(orderId).then((res) => {
        data.orderComments = groupDataByMonthWithStats(res.data);
      });
    };
    // 生态厂商反馈
    const queryEcoparterComment = (dataList, moduleIndex, parsedData) => {
      const operateModule = data.formData.moduleForm[moduleIndex];
      if (!parsedData.fileListDeal) parsedData.fileListDeal = []
      // 获取生态厂商反馈信息列表，allData为每个厂商的反馈记录（同意/拒绝/处理中等）
      let allData = dataList;
      const newTextList = allData.map((item) => {
        return {
          ...item,
          suggest: "", // 增加生态评价字段
          module: parsedData.name,// 解决后台返回生态方第二个缺少模块名的问题
          allParseData: parsedData,
        };
      });
      let num = allData.length;
      data.formData.moduleForm[moduleIndex].textList = JSON.parse(JSON.stringify(newTextList)); // 用于页面展示所有生态厂商反馈信息
      if (operateModule.status == "toSelectFirst") {
        // 如果是省直调度人首次选择合作方，判断是否有反馈信息，决定页面状态
        data.contralSupport = true;// 标记为省直调度人
        operateModule.status = "toSelect";
        const existReject = data.formData.moduleForm[moduleIndex].textList.some((item) => item.dealType == "2");
        if (existReject) {
          // 有拒绝的反馈
          operateModule.status = "reSelectPage";
        }
      }
      // 遍历所有反馈，收集被拒绝的厂商信息，便于后续过滤和重新选择
      allData.forEach((item) => {
        if (item.dealType == "2") { // 2为拒绝
          // 构造被拒绝厂商信息，加入拒绝列表
          let tempInfo = {
            ecopartnerName: item.company,
            contactName: item.contactUserName,
            contactPhone: item.contactPhone,
            userId: item.userId,
          };
          data.rejectCompanyIdlist.push(tempInfo);
          // 构造可重新选择能力方的数据结构
          let ar = [
            {
              name: data.formData.moduleForm[moduleIndex].ecopartnerList[0].name,
              company: data.formData.moduleForm[moduleIndex].ecopartnerList[0].company,
              ownPerson: data.formData.moduleForm[moduleIndex].ecopartnerList[0].ownPerson,
              ownProvince: data.formData.moduleForm[moduleIndex].ownProvince,
            },
          ];
          data.dataCompanyNew = ar;
          // 过滤掉已被拒绝的联系人，优先选可用联系人
          data.dataCompanyNew[0].company.forEach((item) => {
            if (item.contactList) {
              const availableContact = item.contactList.find((contact) =>
                contact.approve === 1 &&
                !data.rejectCompanyIdlist.some((value) => value.userId == contact.userId)
              );
              if (availableContact) {
                item.contactName = availableContact.contactName;
                item.contactPhone = availableContact.contactPhone;
                item.userId = availableContact.userId;
              }
            }
          });
        }
      });
      // 判断最后一个反馈是否为拒绝，若是则进入重新选择能力方页面
      if (allData[num - 1] && allData[num - 1].dealType == "2") {
        if (operateModule.status == "toSelect") {
          operateModule.status = "reSelectPage";
        }
      }
      data.formData.moduleForm[moduleIndex].reSelectData = allData; // // 展示所有反馈信息
      data.formData.moduleForm[moduleIndex].reSelectData.forEach((item) => {
        // 构造表格展示用的info字段
        item.info = {
          ecopartnerName: item.company,
          contactName: item.contactUserName,
          contactPhone: item.contactPhone,
        };
        item.dealTime = item.dealTime;
        item.rejectReason = item.dealContent || '-';
        if (item.dealType == "2") {
          item.dealContent = "拒绝"; // 标记为拒绝
        } else if (item.dealType == "1") {
          item.dealContent = "同意";
        }
      });
      // 判断最后一个反馈是否为同意，若是则进入调度人确认页面
      if (allData[num - 1] && allData[num - 1].dealType == "1") {
        if (data.action !== "dealedAll" && operateModule.status !== "writeScore") {
          operateModule.status = "submitPage";
        }
      }
      // 初始化评分表，仅对未处理的厂商（dealType==0或null）赋默认分数
      allData.forEach((item) => {
        if (item.dealType == "0" || item.dealType == null) {
          data.formData.moduleForm[moduleIndex].tableData1.push({
            companyData: {
              ecopartnerName: item.company,
              contactName: item.contactUserName,
              contactPhone: item.contactPhone,
            },
            satisfiedScore: 1, // 默认满意度分数
            responseScore: 1, // 默认响应分数
            module: data.formData.moduleForm[moduleIndex].name,
          });
        }
      });
      // 评分和结果展示用的最终反馈数据
      // if (allData[num - 1]) {
      //   data.formData.moduleForm[moduleIndex].editFinishData = allData;
      //   data.formData.moduleForm[moduleIndex].editFinishData.forEach((item) => {
      //     item.info = {
      //       ecopartnerName: item.company,
      //       contactName: item.contactUserName,
      //       contactPhone: item.contactPhone,
      //     };
      //     if (item.dealType == "2") {
      //       item.dealContent = "拒绝";
      //     } else if (item.dealType == "1") {
      //       item.dealContent = "同意";
      //     }
      //     item.dealTime = item.dealTime;
      //   });
      // }
      if (Route.query.type == "writeScore") {
        for (let i = 0; i < data.formData.moduleForm.length; i++) {
          // 评分页面去重处理：同一公司多条反馈时，优先保留非拒绝项
          data.formData.moduleForm[i].textList = processDuplicateCompaniesByName(data.formData.moduleForm[i].textList);
          // 同意的排第一，否则按时间最新拒绝的排序
          data.formData.moduleForm[i].textList = sortTextList(data.formData.moduleForm[i].textList)
        }
      }
      // })
    };
    // 获取当前用户所属省级还是市级
    const getPersonList = () => {
      let roleKey = "";
      let org = data.userInfo.orgId;
      // let roleId = data.userInfo.roleIds;
      if (
        org == "2" ||
        org == "3" ||
        org == "4" ||
        org == "5" ||
        org == "6" ||
        org == "7" ||
        org == "8" ||
        org == "9" ||
        org == "10" ||
        org == "11" ||
        org == "12" ||
        org == "13" ||
        org == "14"
      ) {
        roleKey = "cityIndustryCharge";
        data.support_type = "市级";
        data.isProvinceUser = false;
      } else {
        roleKey = "provinceDispatchAdmin";
        data.isProvinceUser = true;
        data.support_type = "省级";
      }
      if (roleKey == "cityIndustryCharge") {
        getAllUserList({ roleKey }).then(res1 => {
          let personList = res1.data || [];

          return getPriviligesData({ roleIds: 72, pageSize: 100 }).then(res2 => {
            const priviligesList = res2.data.rows || [];

            priviligesList.forEach(pUser => {
              const existUser = personList.find(u => u.username === pUser.username);
              if (existUser) {
                if (pUser.remark && pUser.remark.trim() !== "") {
                  existUser.remark = pUser.remark.trim();
                }
              } else {
                personList.push(pUser);
              }
            });

            data.personList = personList;
          });
        });
      } else {
        getAllUserList({ roleKey, scope: 0 }).then((res) => {
          const promises = [Promise.resolve(res)];
          Promise.all(promises).then((results) => {
            data.personList = results.flatMap((result) => result.data);
          });
        });
      }
    };
    getData();
    // 厂商拒绝支撑
    const refuseApply = () => {
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: data.formDataDeal.suggest,
        nextUserIds: data.formData.dispatchUser,
        variables: {
          chooseType: "2",
        },
        chooseType: "2",
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        // data.formDataDeal.suggest = "";
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      });
    };
    // 驳回
    const handleReject = (index) => {
      data.showSuggestCity = true; // 显示驳回原因输入框
      data.operateIndex = index;
    };
    // 调度人驳回
    const backLastComit = () => {
      if (data.suggest == "") {
        message.error("请填写驳回原因！");
        return;
      }
      if (data.support_type == "市级") {
        // 市级调度人驳回
        const operateModule = data.formData.moduleForm[data.operateIndex];
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: data.suggest,
          variables: {
            chooseType: "2",
            cityReject: true,
            needHelp: false,
          },
          chooseType: "2",
        }];
        batchSubmitWithdrawProcess(postData).then((res) => {
          message.success(res.msg);
          data.suggest = "";
          data.showSuggestCity = false
          handlExistTask();
        });
      } else {
        // 省级调度人驳回
        if (data.cityPass) {
          // 省级退回到市级调度人
          const operateModule = data.formData.moduleForm[data.operateIndex];
          const postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: data.suggest,
            nextUserIds: data.formData.dispatchUser,
            variables: {
              chooseType: "2",
              selfSupport: false,
            },
            chooseType: "2",
          }];
          batchSubmitWithdrawProcess(postData).then((res) => {
            message.success(res.msg);
            data.suggest = "";
            data.showSuggestCity = false
            handlExistTask();
          });
        } else if (data.contralSupport) {
          const operateModule = data.formData.moduleForm[data.operateIndex];
          if ((operateModule.status == "toSelect" || operateModule.status == "reSelectPage")) {
            // 省直调度时驳回（省直驳回到申请人）或省直重新调度时
            const postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: data.suggest,
              variables: {
                chooseType: "2",
                needProvinceSupport: false,
              },
              chooseType: "2",
            }];
            batchSubmitWithdrawProcess(postData).then((res) => {
              message.success(res.msg);
              data.suggest = "";
              data.showSuggestCity = false
              handlExistTask();
            });
          }
        } else {
          // 省级驳回到省直
          const operateModule = data.formData.moduleForm[data.operateIndex];
          const postData = [{
            taskId: operateModule.taskId,
            procInsId: operateModule.procInstId,
            comment: data.suggest,
            nextUserIds: data.dispatchProvinceUser,
            variables: {
              chooseType: "2",
              selfSupport: false,
            },
            chooseType: "2",
          }];
          batchSubmitWithdrawProcess(postData).then((res) => {
            message.success(res.msg);
            data.suggest = "";
            data.showSuggestCity = false
            handlExistTask();
          });
        }
      }
    };
    const addCooperate = (value, index) => {
      data.companyId = value.name;
      data.showAdd = true;
      data.formData.moduleForm[index].companyIdList = value.company.map(item => { return item.enterpriseId });
      data.currentModuleIndex = index;
    };
    const closeAdd = () => {
      data.isSyncAuth = false;
      data.showAdd = false;
      data.contanctList = [];
      data.CooperateData = {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        duty: undefined,
        area: undefined,
        enterpriseId: undefined,
      };
    };
    const ecologyChangeOld = (val) => {
      data.CooperateData.contanct = null;
      data.CooperateData.phone = null;
      let list = data.teamOldList.filter((item) => {
        return item.name == val;
      });
      data.CooperateData.sync = list[0].sync;
      data.CooperateData.auth = list[0].auth;
      data.CooperateData.totalScore = list[0].totalScore;
      data.CooperateData.area = list[0].address != null ? list[0].address : "江苏省";
      data.contanctList = list[0].contactList;
      if (data.formData.moduleForm[data.currentModuleIndex].status == "reSelectPage") {
        data.CooperateData.enterpriseId = list[0].enterpriseId;
      }
      data.comId = list[0].enterpriseId;
      if (data.CooperateData.sync == 1 && data.CooperateData.auth == 1) {
        data.isSyncAuth = false;
      } else {
        data.isSyncAuth = true;
      }
    };
    // 新增生态厂商时的搜索函数
    const handleSearch = debounce((val) => {
      fetching.value = true;
      selectTree({ name: val }).then((res) => {
        fetching.value = false;
        const mockData = res.data.filter((item) => {
          return item.id != "1189";
        });
        data.teamOldList = mockData;
        displayOptions.value = mockData;
      });
    }, 600);
    function debounce(fn, delay) {
      let time = null;
      return function () {
        let context = this;
        let args = arguments;
        if (time) {
          clearTimeout(time);
        }
        time = setTimeout(() => {
          // fn.call(this)
          fn.apply(context, args);
        }, 600);
      };
    }
    const selectUser = (val) => {
      let tempInfo = data.contanctList;
      const result = tempInfo.filter((item) => {
        return item.contactName === val;
      });
      data.CooperateData.phone = result[0].contactPhone;
      data.CooperateData.enterpriseId = result[0].enterpriseId;
      data.CooperateData.area = result[0].contactAddress ? result[0].contactAddress : data.CooperateData.area;
      data.CooperateData.userId = result[0].userId;
      data.CooperateData.approve = result[0].approve;
    };
    const selectUserCom = (value, item) => {
      item.contactName = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      ).contactName;
      const selectedCompany = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      );
      if (selectedCompany) {
        item.contactPhone = selectedCompany.contactPhone;
      }
      item.userId = item.contactList.find(
        (opt) => opt.userId === value || opt.contactName === value
      ).userId;
      // data.selectCompanyId = item.ecopartnerId;
      // data.selectPhone = null;
    };
    // 新增生态厂商
    const submitAdd = async () => {
      try {
        await addFormRef.value?.validate();
        if (data.formData.moduleForm[data.currentModuleIndex].status == "reSelectPage") {
          let com = {
            ecopartnerName: data.CooperateData.company,
            contactPhone: data.CooperateData.phone,
            contactName: data.CooperateData.contanct,
            userId: data.CooperateData.userId,
            contactList: data.contanctList,
            enterpriseId: data.CooperateData.enterpriseId,
            sync: data.CooperateData.sync,
            auth: data.CooperateData.auth,
            approve: data.CooperateData.approve,
            totalScore: data.CooperateData.totalScore,
          };
          data.dataCompanyNew[0].company.push(com);
        } else if (data.action == "edit") {
          let com = {
            ecopartnerName: data.CooperateData.company,
            contactPhone: data.CooperateData.phone,
            contactName: data.CooperateData.contanct,
            userId: data.CooperateData.userId,
            contactList: data.contanctList,
            enterpriseId: data.CooperateData.enterpriseId,
            sync: data.CooperateData.sync,
            auth: data.CooperateData.auth,
            approve: data.CooperateData.approve,
            totalScore: data.CooperateData.totalScore,
          };
          data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.push(com); // 生态厂商list新增
          data.formData.moduleForm[data.currentModuleIndex].companyIdList = data.formData.moduleForm[data.currentModuleIndex].editDataCompany[0].company.map((item) => { return item.enterpriseId; });
          data.CooperateData = {
            company: undefined,
            contanct: undefined,
            phone: undefined,
            area: undefined,
          };
          data.showAdd = false;
        } else {
          data.formData.moduleForm[data.currentModuleIndex].ecopartnerList.forEach((item) => {
            if (item.name === data.companyId) {
              let com = {
                ecopartnerName: data.CooperateData.company,
                contactPhone: data.CooperateData.phone,
                contactName: data.CooperateData.contanct,
                userId: data.CooperateData.userId,
                contactList: data.contanctList,
                enterpriseId: data.CooperateData.enterpriseId,
                sync: data.CooperateData.sync,
                auth: data.CooperateData.auth,
                approve: data.CooperateData.approve,
                totalScore: data.CooperateData.totalScore,
              };
              item.company.push(com);
            }
          });
        }
        message.success("添加成功");
        data.formData.moduleForm[data.currentModuleIndex].companyIdList = data.formData.moduleForm[data.currentModuleIndex].ecopartnerList[0].company.map((item) => { return item.enterpriseId; });
        data.showAdd = false;
        data.CooperateData = {
          company: undefined,
          contanct: undefined,
          phone: undefined,
          area: undefined,
        };
      } catch (error) { }
    };
    const dealNum = (v) => {
      if (v.dealType !== "2" && v.dealType !== "1") {
        return (v.satisfiedScore = "-");
      } else {
        if (v.satisfiedScore) {
          return (v.satisfiedScore = v.satisfiedScore);
        } else {
          return (v.satisfiedScore = "-");
        }
      }
    };
    const dealNumNew = (v) => {
      if (v.dealType !== "2" && v.dealType !== "1") {
        return (v.responseScore = "-");
      } else {
        if (v.responseScore) {
          return (v.responseScore = v.responseScore);
        } else {
          return (v.responseScore = "-");
        }
      }
    };
    // 生态厂商单选事件
    const onCheckChange = (e, item, index, type) => {
      const { value } = e.target;
      const { action } = data;
      if (action === "edit" || data.formData.moduleForm[index].status === "toSelect" || data.formData.moduleForm[index].status === "reStar") {
        data.formData.moduleForm[index].selectCompanyList = item;
        data.formData.moduleForm[index].ipartnerId = item.userId;
        data.formData.moduleForm[index].selectId = value;
        data.formData.moduleForm[index].selectIdOwn = value;
        data.formData.moduleForm[index].selectPhone = value;
      } else {
        data.reSelectCompany = item;
        data.formData.moduleForm[index].ipartnerId = item.userId;
        data.formData.moduleForm[index].enterpriseId = item.enterpriseId;
        data.formData.moduleForm[index].selectId = value;
        data.formData.moduleForm[index].selectIdOwn = value;
        data.formData.moduleForm[index].selectPhone = value;
      }
    };
    const handleCloseSugget = () => {
      data.showSuggest = false;
      data.formDataDeal.fileListDeal = [];
      data.formDataDeal.suggest = "";
      data.formDataDeal.supportCos = "";
      data.companyAload = true;
    };
    // 支撑反馈事件
    const handleOkSugget = async () => {
      await dealFormRef.value?.validate();
      data.showSuggest = false;
      if (data.companyAload) {
        // 同意反馈
        agreeApply();
      } else {
        // 拒绝反馈
        refuseApply();
      }
    };
    const agreeApply = () => {
      // 合作方处理同意
      const operateModule = data.formData.moduleForm[data.operateIndex];
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: data.formDataDeal.suggest,
        variables: {
          chooseType: "1",
          customDataForm: JSON.stringify({ ...operateModule.textList[0].allParseData, ...data.formDataDeal }),
        },
      }];
      batchSubmitWithdrawProcess(postData).then((res) => {
        message.success(res.msg);
        data.suggest = "";
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      }).finally(() => {
        data.addLoading = false;
      });
    }
    const submitCompany = () => {
      data.showSuggest = true;
      data.companyAload = true; // 同意
      data.formDataDeal.suggest = "";
      data.refuseOpen = false;
      data.isThirdEco = data.userInfo.roleKeyList.includes('ecologicalPartner') ? true : false; // 是否生态厂商
      if (data.isThirdEco) {
        // 生态厂商不需要填写支撑人/天数
        data.dealRules.supportCos = [
          {
            required: false,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ];
        data.dealRules.suggest = [
          {
            required: false,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ];
      } else {
        // 自有能力方、自有联系人需要填写支撑人/天数
        data.dealRules.supportCos = [
          {
            required: true,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ];
        data.dealRules.suggest = [
          {
            required: true,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ];
      }
    };
    const refuseApplyCompany = () => {
      data.showSuggest = true;
      data.companyAload = false; // 拒绝
      data.formDataDeal.suggest = "";
      data.refuseOpen = true;
      const isThirdEcoTemp = data.userInfo.roleKeyList.includes('ecologicalPartner') ? true : false; // 是否生态厂商
      if (isThirdEcoTemp) {
        data.dealRules.suggest = [
          {
            required: false,
            message: "请输入拒绝原因",
            trigger: "change",
          },
        ];
      } else {
        data.dealRules.suggest = [
          {
            required: true,
            message: "请输入拒绝原因",
            trigger: "change",
          },
        ];
      }
    };
    // 调度完成
    // const submitBtn = (index) => {
    //   data.showSuggest = true;
    //   data.formDataDeal.suggest = "";
    //   data.operateIndex = index;
    // };
    const controlPro = (value, index) => {
      // data.selectId = null;
      data.formData.moduleForm[index].provinceUser = null;
      data.provincePersonList = []
      if (value) {
        let roleKey = "provinceIndustryCharge";
        getPerson(roleKey).then((res) => {
          data.provincePersonList = res.data;
        });
      }
    };
    const formatContent = (content) => {
      if (!content) return "";
      content = content.replace(/级/g, "");
      return content;
    };
    const formatRealName = (realName, remark) => {
      if (!realName) return "";
      realName = realName.replace(/级/g, "");
      if (realName.includes("王达伟")) {
        return "王达伟(省公司交通、融合创新行业支撑负责人)";
      }
      if (realName.includes("仲伟奇")) {
        return "仲伟奇(省公司农业文旅行业支撑负责人)";
      }
      if (realName.includes("孙晓星")) {
        return "孙晓星(省公司政企行业支撑负责人)";
      }
      if (realName.includes("魏宇珺")) {
        return "魏宇珺(省公司教育行业支撑负责人)";
      }
      if (realName.includes("丁德胜")) {
        return "丁德胜(省公司医疗行业支撑负责人)";
      }
      if (realName.includes("徐剑宏")) {
        return "徐剑宏(省公司政法公安行业支撑负责人)";
      }
      if (realName.includes("肖明")) {
        return "肖明(省公司党政行业支撑负责人)";
      }
      if (realName.includes("许文杰")) {
        return "许文杰(省公司金融、互联网行业支撑负责人)";
      }
      if (realName.includes("吴鹏")) {
        return "吴鹏(省公司工业行业支撑负责人)";
      }
      if (realName.includes("戴云平")) {
        return "戴云平(省直项目交付团队负责人)";
      }
      if (remark && remark.trim() !== "") {
        return `${realName}(${remark.trim()})`;
      }

      return realName;
    };
    const disabledDate = (current) => {
      const today = dayjs().startOf("day");
      return current && current < today;
    };
    // 获取新增生态厂商时的可选项数据
    const fetchData = async () => {
      fetching.value = true;
      try {
        const response = await selectTree();
        const mockData = response.data.filter((item) => {
          return item.id != "1189";
        });
        data.teamOldList = mockData || [];
        displayOptions.value = mockData;
      } catch (error) {
        console.error("Failed to fetch options:", error);
        // 可以在这里设置错误状态或显示错误消息
        displayOptions.value = [];
      } finally {
        fetching.value = false;
      }
    };
    onMounted(fetchData);
    const viewFileData = (view) => {
      data.viewLoading = view;
    };
    const changeRadio = (e) => {
      if (e.target.value == "1") {
        data.isSubmit = true;
      } else {
        data.isSubmit = false;
        data.formDataScore.deliveryManager = undefined;
      }
    };
    const download = (file) => {
      const href = file.url || file.fileUrl;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = href;
      if (href.includes(windowOrigin)) {
        newHref = "/portal" + href.split(windowOrigin)[1];
      }
      window.open(windowOrigin + newHref + "?token=" + token);
      return false;
    };
    const view = (file) => {
      data.viewLoading = true;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      pptTopdf({
        filePath: file.path,
      }).then((res) => {
        if (res.code == 200) {
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1];
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(
                windowOrigin + newHref + "?token=" + token
              ),
              urlName: file.name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      }).finally(() => {
        data.viewLoading = false;
      });
      return false;
    };
    const handleEcopartnerInfo = async (phone) => {
      try {
        const val = await getAllUserList({ phone, scope: 0 });
        if (!val.data?.[0]) return;
        const orgInfo = val.data[0].orgAllName?.split(",") || [];
        if (orgInfo.length === 2 && orgInfo[1] === "苏移集成") {
          return "江苏移动信息系统集成有限公司";
        }
        return orgInfo[1] || val.data[0].orgName;
      } catch (error) {
        console.error("获取生态能力方信息失败:", error);
        return null;
      }
    };
    const handleCloseScore = () => {
      data.showScore = false;
      data.formDataScore.deliveryManager = undefined;
      data.formDataScore.isTurn = "2";
      data.isSubmit = false;
    };
    // 评分完成后，是否转售中的弹窗确定事件
    const handleOkScore = async () => {
      await scoreFormRef.value?.validate();
      if (data.formDataScore.projectCode && !/^\d+$/.test(data.formDataScore.projectCode)) {
        message.warning("项目编号应为纯数字，且不能为负数或包含字母");
        data.addLoading = false;
        return;
      }
      // 先评分
      const operateModule = data.formData.moduleForm[data.operateIndex];
      // 只提交同意的且没有评过分的生态方数据
      const ownPerson = operateModule.textList.filter((item) => !item.enterpriseId && (!item.scored && item.dealType == '1')); // enterpriseId为null是自有联系人或自有能力方，不需要打分，需要生态评价
      const thirdPerson = operateModule.textList.filter((item) => item.enterpriseId && (!item.scored && item.dealType == '1')); // enterpriseId非null是生态厂商，需要打分，需要生态评价
      // 移除allParseData
      const filteredOwnPerson = ownPerson.map(({ allParseData, ...rest }) => rest);
      const filteredThirdPerson = thirdPerson.map(({ allParseData, ...rest }) => rest);
      const postData = [{
        taskId: operateModule.taskId,
        procInsId: operateModule.procInstId,
        comment: "评分完毕",
        variables: {
          addScoreInfo: JSON.stringify({ writeScore: ownPerson.length > 0 ? filteredOwnPerson : filteredThirdPerson }),
        },
      }];
      try {
        // 先进行评分
        await batchSubmitWithdrawProcess(postData);
        // 再转售中
        const postData2 = {
          orderId: Route.query.orderId,
          projectCode: data.formDataScore.projectCode,
          projectName: data.formDataScore.projectName,
          deliveryManager: data.formDataScore.deliveryManager,
          sendMsg: data.formDataScore.isTurn == "1" ? true : false,
        }
        await midDispatch(postData2);
        message.success("操作成功");
        handlExistTask();
        // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog";
      } catch (error) {
        console.error("操作失败:", error);
        message.error("操作失败");
        data.addLoading = false;
      }
    };
    const handleCloseSuggeOld = () => {
      data.showSuggestCity = false;
      data.companyAload = !data.companyAload;
    };
    const filterOption = (input, option) => {
      return option.label.indexOf(input) >= 0;
    };
    const toCompanyDetail = (item) => {
      if (item.ecopartnerId > 10000) {
        window.open(
          "https://ipartner.jsdict.cn/static/detail?partner_id=" + item.ecopartnerId + "&token=bhubh3333ugy", "_blank"
        );
      } else {
        window.open(
          "https://ipartner.jsdict.cn/static/detail?partner_id=" + item.enterpriseId + "&token=bhubh3333ugy", "_blank"
        );
      }
    };
    const closeModal = () => {
      data.moduleTypeVisible = false;
    };
    const showModuleTypeDialog = (index) => {
      data.moduleTypeVisible = true;
      data.currentModuleIndex = index;
    };
    // 模块选择弹窗确定事件
    const handleSelectModule = (moduleType) => {
      const index = data.currentModuleIndex;
      const existModule = data.formData.moduleForm.some(item =>
        item.name == moduleType.name && item.uid == moduleType.id
      );
      if (existModule) {
        message.warning("该模块已存在");
        return;
      }
      if (typeof index === 'number' && data.formData.moduleForm[index]) {
        data.formData.moduleForm[index].uid = moduleType.id;
        data.formData.moduleForm[index].name = moduleType.name;
        data.formData.moduleForm[index].intro = moduleType.intro || moduleType.description || moduleType.summary || moduleType.abilityIntro || ""; '';
        formatEcopartnerList(index, moduleType);
      }
      data.moduleTypeVisible = false;
      const exitMainModule = data.formData.moduleForm.find((item) => {
        return item.businessModuleType === '1' && item.name;
      });
      if (exitMainModule) {
        // 有方案，则工单标题以方案开头
        data.formData.title = "关于" + exitMainModule.name + "的售前支撑工单";
      } else {
        for (let i = 0; i < data.formData.moduleForm.length; i++) {
          if (data.formData.moduleForm[i].name) {
            // 没有方案，则工单标题以第一个支撑模块开头
            data.formData.title = "关于" + data.formData.moduleForm[i].name + "的售前支撑工单";
            break;
          }
        }
      }
    };
    const changeBusinessModuleType = (val, index) => {
      const module = data.formData.moduleForm[index];
      if (module) {
        module.uid = null;
        // module.projectContent = '';
        // module.fileList = [];
        module.businessModuleType = val.target.value;
        module.name = '';
        module.intro = '';
        module.ecologyType = '';
        module.selectUsers = [];
        if (module.editDataCompany?.[0]) {
          const firstItem = module.editDataCompany[0];
          firstItem.company = [];
          firstItem.ownPerson = [];
          firstItem.ownProvince = [];
        }
      }
    };
    // 删除支撑模块
    const handleDeleteModule = (index) => {
      if (data.formData.moduleForm.length > 1) {
        data.formData.moduleForm.splice(index, 1);
      }
    };
    // 新增支撑模块
    const addModule = () => {
      data.formData.moduleForm.push({
        uid: null,
        status: 'edit',
        procInstId: "",
        taskId: "",
        // editFinishData: [], // 对生态厂商的评分
        textList: [], // 生态厂商列表
        ownProvince: [], //自有联系人list
        tableData1: [],
        projectContent: "",
        fileList: [],
        businessModuleType: "1",
        name: "",
        intro: "",
        selectUsers: [],
        ecologyType: "",
        editDataCompany: [],
        reSelectData: [],
        ecopartnerList: [],
        selectCompanyList: {},
        ipartnerId: "",
        selectId: "",
        selectIdOwn: "",
        selectPhone: "",
        needProvince: false, // 是否申请省级调度支撑
        isBySelf: "2", // 1自己支撑2不自己支撑
      });
    };
    const limitLength = (e) => {
      if (e.target.value.length > 30) {
        data.formData.projectCode = e.target.value.slice(0, 30);
      }
    };
    const reSubmit = async (index) => {
      try {
        await mainFormRef.value?.validate();
        data.addLoading = true;
        if (data.formData.projectCode) {
          if (data.formData.projectCode.includes("-")) {
            message.warning("项目编号应为纯数字");
            data.addLoading = false;
            return;
          }
        }
        data.formData.time = dayjs(data.formData.time).format("YYYY-M-D");
        const operateModule = data.formData.moduleForm[index];
        if (data.rejectCompanyIdlist.some((value) => value.userId == operateModule.selectCompanyList.userId)) {
          message.warning("该厂商联系人已拒绝，请重新选择");
          data.addLoading = false;
          return
        }
        operateModule.projectName = data.formData.projectName;
        operateModule.projectCode = data.formData.projectCode;
        operateModule.dispatchUser = data.formData.dispatchUser;
        operateModule.userPhone = data.formData.userPhone;
        operateModule.userInfo = data.formData.userInfo;
        operateModule.supportMehod = data.formData.supportMehod;
        operateModule.time = data.formData.time;
        const { editDataCompany, ...moduleFormWithoutEcopartner } = operateModule;// 移除moduleForm的editDataCompany
        const postData = [{
          taskId: operateModule.taskId,
          procInsId: operateModule.procInstId,
          comment: "重新提交",
          nextUserIds: data.formData.dispatchUser,
          variables: {
            customDataForm: JSON.stringify(moduleFormWithoutEcopartner),
            nextUserIds: data.formData.dispatchUser,
            isProvince: data.isProvinceUser,
          },
        }];
        batchSubmitWithdrawProcess(postData).then((res) => {
          message.success("提交成功");
          handlExistTask();
          // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
        }).finally(() => {
          data.addLoading = false;
        });
      } catch (error) {
        console.log(error);
        data.addLoading = false;
      }
    };
    const endWork = (index) => {
      data.operateIndex = index;
      const operateModule = data.formData.moduleForm[data.operateIndex];
      if (operateModule.status == "reStar") {
        // 调度人驳回，发起人结束流程
        querytOrderById(Route.query.orderId).then((res) => {
          const taskList = res.data || [];
          if (taskList.length > 1) {
            // 不是最后一个结束的模块
            // 只提交同意的且没有评过分的生态方数据
            const postData = [{
              taskId: operateModule.taskId,
              procInsId: operateModule.procInstId,
              comment: "发起人结束调度",
            }];
            cityBack(postData).then((res) => {
              message.success(res.msg);
              handlExistTask();
              // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
            });
          } else {
            // 最后一个结束的模块，弹售中弹窗
            if (taskList.length == 1 && taskList[0].taskId == operateModule.taskId) {
              data.formDataScore.projectCode = data.formData.projectCode;
              data.formDataScore.projectName = data.formData.projectName;
              data.formDataScore.deliveryManager = undefined;
              data.formDataScore.isTurn = "2";
              data.showScore = true;
              data.addLoading = false;
            } else {
              const postData = [{
                taskId: operateModule.taskId,
                procInsId: operateModule.procInstId,
                comment: "发起人结束调度",
              }];
              cityBack(postData).then((res) => {
                message.success(res.msg);
                handlExistTask();
                // window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
              });
            }
          }
        });
      } else {
        // 调度人自己支撑
        handleWriteScore(operateModule);
      };
    };
    return {
      ...toRefs(data),
      mainFormRef,
      Router,
      Route,
      addFormRef,
      selectedValues,
      displayOptions,
      fetching,
      dealFormRef,
      scoreFormRef,
      businessModuleTypeComputed,
      changeBusinessModuleType,
      reSubmit,
      endWork,
      handleDeleteModule,
      handleSelectModule,
      showModuleTypeDialog,
      closeModal,
      toCompanyDetail,
      filterOption,
      backLastComit,
      handleCloseSuggeOld,
      handleOkScore,
      handleCloseScore,
      changeRadio,
      download,
      view,
      viewFileData,
      disabledDate,
      dealNumNew,
      // submitBtn,
      controlPro,
      handleCloseSugget,
      handleOkSugget,
      submitCompany,
      refuseApplyCompany,
      cancel,
      submitAdd,
      selectUserCom,
      handleReject,
      dealNum,
      onCheckChange,
      selectUser,
      ecologyChangeOld,
      handleSearch,
      addCooperate,
      closeAdd,
      submit,
      createOrder,
      formatRealName,
      formatContent,
      addModule,
      limitLength,
    };
  },
});
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>
<style lang="scss">
.resize-table-header-line.el-table {

  // 默认表头和单元格使用默认光标
  th.el-table__cell,
  td>.cell {
    cursor: default !important;
  }

  // 只在表头分隔线位置显示调整列宽光标
  th.el-table__cell {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 8px; // 分隔线热区宽度
      height: 100%;
      cursor: default;
      transform: translateX(50%); // 居中显示
    }

    &:hover::after {
      cursor: col-resize !important;
    }
  }
}
</style>
