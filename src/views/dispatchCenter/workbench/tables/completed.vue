<template>
  <div class="qilin-table">
    <div class="qilin-table-content">
      <QilinTable
        v-model:tableConfig="tableConfig"
        :loading="tableLoading"
        @changeCurrentPage="changeCurrentPage"
        @changeCurrentSize="changeCurrentSize"
        @rowClickData="clickRowData"
      >
        <template v-slot:title="slotData">
          <div
            v-html="getTitle(slotData.data.scope.row)"
            style="
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
          ></div>
        </template>
        <template v-slot:typeName="slotData">
          <div v-html="getTypeName(slotData.data.scope.row)"></div>
        </template>
        <template v-slot:taskName="slotData">
          <div v-if="slotData.data.scope.row.assigneeId == null">已审核</div>
          <div v-else>{{ slotData.data.scope.row.taskName }}</div>
        </template>
        <template v-slot:assigneeName="slotData">
          <a-popover title="" :trigger="['hover', 'focus']">
            <template #content>
              <p style="margin-bottom: 0">
                联系电话：{{ dealData(slotData.data.scope.row.assigneePhone) }}
              </p>
              <p style="margin-bottom: 0">
                所属部门：{{ dealData(slotData.data.scope.row.assigneeOrgName) }}
              </p>
            </template>
            {{ dealData(slotData.data.scope.row.assigneeName) }}
          </a-popover>
        </template>
        <template v-slot:status="slotData">
          <div :class="statusClass(slotData.data.scope.row)">
            {{ getProcess(slotData.data.scope.row) }}
          </div>
        </template>
        <template v-slot:businessType="slotData">
          <div v-html="getBusinessTypeName(slotData.data.scope.row)"></div>
        </template>
        <template v-slot:provider="slotData">
          {{ providerWith(slotData.data.scope.row.startOrgName) }}
        </template>
      </QilinTable>
    </div>
  </div>
</template>

<script setup>
import {
  getDispatchCompletedData,
  getClassificationData,
} from "@/api/dispatchCenter/backlog&completed.js";
import { useRouter } from "vue-router";
import { ref, reactive } from "vue";
import QilinTable from "../components/table.vue";
import { message } from "ant-design-vue";
const props = defineProps({
  searchParams: {
    type: Object,
    default: () => ({}),
  },
});
const router = useRouter();

const tableLoading = ref(false);
// 计算属性
const getTitle = computed(() => (data) => {
  return data.title || "-";
});

const getTypeName = computed(() => (data) => {
  const typeMap = {
    1: "上架审核",
    0: "下架审核",
    2: "能力撰写",
    8: "售前调度工单",
    4: "售前调度工单",
    9: "售中调度工单",
  };
  return typeMap[data.type] || "";
});

const getBusinessTypeName = computed(() => (data) => {
  const businessTypeMap = {
    1: "解决方案",
    2: "自有能力",
    3: "场景",
    4: "标准产品",
    5: "专区",
    6: "政策",
    7: "撰写",
    11: "调度支撑",
  };
  return businessTypeMap[data.businessType] || "";
});

const getProcess = computed(() => (row) => {
  if (row.assigneeName === null) return "已完成";
  return "审核中";
});
const dealData = (v) => {
  if (v) {
    return v;
  } else {
    return "-";
  }
};
// 工具方法
const providerWith = (value) => {
  if (!value) return "";
  const parts = value.split("/");
  const [first, second, ...rest] = parts;
  if (first === "江苏公司") {
    return rest.length > 0 ? `${second}/${rest.join("/")}` : second;
  }
  return value;
};

const statusClass = (row) => {
  const status = getProcess.value(row);
  return {
    returnBack: status === "已驳回",
    inCheck: status === "审核中",
    complete: status === "已完成",
  };
};
const clickRowData = (row) => {
  try {
    if (row.type == 8) {
      // const json = JSON.parse(row.procVars.customDataForm);
      const routeName =
        row.procDefName === "售前调度工单" ? "endWork" : "inWorked";
      router.push({
        name: routeName,
        query: {
          id: row.procInstId,
          type: "dealedAll",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心",
          orderId: row.id,
        },
      }); 
      return;
    }
    if (row.type == 9) {
      // 售中调度路由跳转
      router.push({
        name: "coordination",
        query: {
          orderId: row.id,
          action: "detail",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心"
        }
      });
      return;
    }
  } catch (error) {
    console.error("路由跳转失败:", error);
    message.error("查看详情失败");
  }
};

// 分页处理
const changeCurrentPage = (page) => {
  tableConfig.paginationsObj.currentPage = page;
  getTableData(props.searchParams);
};

const changeCurrentSize = (size) => {
  tableConfig.paginationsObj.pageSize = size;
  getTableData(props.searchParams);
};

const tableConfig = reactive({
  elTableConfig: {
    border: false,
  },
  headerConfig: [
    {
      label: "工单标题",
      prop: "title",
      type: "slot",
      slotName: "title",
      align: "center",
      tooltipOptions: true,
      width: 460,
    },
    {
      label: "工单类型",
      prop: "type",
      type: "slot",
      slotName: "typeName",
      align: "center",
      width: 160,
    },
    {
      label: "业务模块",
      prop: "businessType",
      type: "slot",
      slotName: "businessType",
      align: "center",
      width: 160,
    },
    {
      label: "工单流程",
      prop: "taskName",
      type: "slot",
      slotName: "taskName",
      align: "center",
      width: 150,
    },
    {
      label: "处理人",
      prop: "assigneeName",
      type: "slot",
      slotName: "assigneeName",
      align: "center",
      width: 86,
    },
    {
      label: "工单状态",
      prop: "status",
      type: "slot",
      slotName: "status",
      align: "center",
      width: 86,
    },
    {
      label: "提交方",
      prop: "provider",
      type: "slot",
      slotName: "provider",
      align: "center",
      width: 150,
    },
    {
      label: "提交人员",
      prop: "startUserName",
      type: "text",
      align: "center",
      width: 160,
    },
    {
      label: "提交时间",
      prop: "createTime",
      type: "text",
      align: "center",
      width: 160,
    },
    {
      label: "办理时间",
      prop: "finishTime",
      type: "text",
      align: "center",
      width: 160,
    },
    {
      label: "操作",
      type: "operate",
      align: "center",
      width: 100,
      fixed: "right",
      hideSeparate: true,
      operateOptions: [
        {
          buttonName: "详情",
          buttonType: "primary",
          buttonSize: "default",
          text: true,
          buttonEvent: clickRowData,
        },
      ],
    },
  ],
  tableData: [],
  paginationsObj: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 30],
    total: 0,
    layout: "total,sizes,prev,pager,next,jumper",
  },
});

const getTableData = (searchData = {}) => {
  tableLoading.value = true;
  let params = {
    pageNo: tableConfig.paginationsObj.currentPage,
    pageSize: tableConfig.paginationsObj.pageSize,
  };
  if (searchData) {
    Object.keys(searchData).forEach((key) => {
      const val = searchData[key];
      if (val !== undefined && val !== null && (val || val === 0)) {
        params[key] = val;
      }
    });
  }
  const paramsOld = {
    pageNum: 1,
    pageSize: 10,
  };
  getClassificationData(paramsOld)
    .then((res) => {
      if (res.code !== 200) {
        return Promise.reject(new Error(res.msg || "分类数据获取失败"));
      }
      const rows = res.data.rows || [];
      const targetCategory = rows.find(
        (item) => item.categoryName === "售前调度工单"
      );
      if (!targetCategory) {
        return Promise.reject(new Error("未找到售前调度工单分类"));
      }
      params.category = targetCategory.code;
      return getDispatchCompletedData(params);
    })
    .then((res) => {
      if (res.code === 200) {
        tableConfig.tableData = res.data.rows || [];
        tableConfig.paginationsObj.total = res.data.totalRows || 0;
      } else if (res.code !== 401) {
        message.error(res.msg || "系统错误");
      }
      tableLoading.value = false;
    })
    .catch((err) => {
      console.error(err);
      tableLoading.value = false;
      message.error(err.message || "请求失败");
    });
};
watch(
  () => props.searchParams,
  (newVal) => {
    tableConfig.paginationsObj.currentPage = 1;
    getTableData(newVal);
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.qilin-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column nowrap;

  > .qilin-table-content {
    background-color: #fff;
    padding: 16px 20px;
    border-radius: 4px;
    flex: 1;
    display: flex;
    flex-flow: column nowrap;
    overflow: hidden;

    :deep(.el-table) {
      .inCheck {
        width: 60px;
        background: rgba(246, 118, 0, 0.1);
        color: #f67600;
      }
      .complete {
        width: 60px;
        background: rgba(0, 189, 98, 0.1);
        color: #00bd62;
      }
      .returnBack {
        width: 60px;
        background: rgba(245, 29, 15, 0.1);
        color: #f51d0f;
      }
    }
  }
}
</style>