<template>
  <div class="pre-sale-container">
    <PreSearchBar ref="searchBarRef" @form-search="handleSearch" class="search-form" />
    <div class="toggle-actions">
      <a-radio-group v-model:value="activeStatus" button-style="solid" class="status-toggle">
        <a-radio-button value="todo">待办</a-radio-button>
        <a-radio-button value="done">已办</a-radio-button>
        <!-- <a-radio-button value="all" v-if="isAdmin">全部</a-radio-button> -->
      </a-radio-group>
      <div class="btn-group">
        <!-- <a-button class="default_btn" @click="exportEx()" v-if="!isEcologicalPartner">导出</a-button> -->
        <a-button class="default_btn" @click="openExportModal" v-if="!isEcologicalPartner">导出</a-button>
        <a-button type="primary" class="apply-support-btn" @click="applySupport"
          v-if="!isEcologicalPartner">申请支撑</a-button>
      </div>
    </div>
    <div class="table-area">
      <component :is="currentComponent" :search-params="searchParams" />
    </div>
    <DispatchTypeModal v-model="showDispatchModal" @select="handleDispatchSelect" />
    <a-modal v-model:visible="showExportModal" title="报表导出" width="400px" style="top: 20%" :footer="null" @close="handleExportCancel">
      <a-form labelAlign="right">
        <a-form-item label="工单类型选择" required>
          <a-select v-model:value="selectedExportType" placeholder="请选择工单类型" :options="orderTypes" allowClear />
        </a-form-item>
      </a-form>
      <div class="flex just-center margin_t_32">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleExportCancel">取消</a-button>
        <a-button class="custom_btn active_btn" type="primary" :disabled="!selectedExportType"
          @click="handleExportConfirm">
          确定
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch ,nextTick } from "vue";
import Backlog from "../tables/backlog.vue";
import Completed from "../tables/completed.vue";
import PreAllList from "../tables/preAll.vue";
import PreSearchBar from "./PreSearchBar.vue";
import DispatchTypeModal from "@/components/DispatchTypeModal/index.vue";
import { message } from "ant-design-vue";
import {
  getClassificationData,
  processDispatchTodoListExport,
  processDispatchFinishedListExport,
  processPreTotalListExport,
} from "@/api/dispatchCenter/backlog&completed.js";
const isAdmin = computed(() => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  return (
    userInfo && userInfo.roleKeyList && userInfo.roleKeyList.includes("admin")
  );
});
const isEcologicalPartner = computed(() => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  return userInfo?.roleKeyList?.includes("ecologicalPartner");
});
const STORAGE_KEY_PRE_ACTIVE_STATUS = "dispatch_center_pre_activeStatus";
const activeStatus = ref(
  sessionStorage.getItem(STORAGE_KEY_PRE_ACTIVE_STATUS) || "todo"
);
watch(activeStatus, (newVal) => {
  sessionStorage.setItem(STORAGE_KEY_PRE_ACTIVE_STATUS, newVal);
});
const searchParams = ref({});
const searchBarRef = ref(null);
const processTypeInfo = ref(null);

// 导出弹窗显示状态
const showExportModal = ref(false);
const selectedExportType = ref(null);

// 工单类型选项，根据实际调整
const orderTypes = [
  { label: "售前调度工单", value: 8 },
  { label: "售中调度工单", value: 9 },
  // { label: "售后调度工单", value: "" },
];
// 调度中心弹窗显示状态
const showDispatchModal = ref(false);
const currentComponent = computed(() => {
  switch (activeStatus.value) {
    case "todo":
      return Backlog;
    case "done":
      return Completed;
    // case "all":
    //   return PreAllList;
    default:
      return Backlog;
  }
});

const handleSearch = (formData) => {
  searchParams.value = { ...formData };
};
const exportApiMap = {
  todo: processDispatchTodoListExport,
  done: processDispatchFinishedListExport,
  // all: isAdmin.value ? processPreTotalListExport : null,
};
const openExportModal = () => {
  showExportModal.value = true;
  if (
    searchParams.value.type &&
    orderTypes.some((item) => item.value === searchParams.value.type)
  ) {
    selectedExportType.value = searchParams.value.type;
  } else {
    selectedExportType.value = null;
  }
};
const handleExportConfirm = () => {
  if (!selectedExportType.value) {
    message.warning("请选择工单类型");
    return;
  }
  showExportModal.value = false;

  let exportApi = exportApiMap[activeStatus.value];
  if (!exportApi) {
    message.error("当前状态不支持导出");
    return;
  }
  let params = {
    category: processTypeInfo.value?.code,
    ...searchParams.value,
    type: selectedExportType.value,
  };
  exportApi(params)
    .then((res) => {
      const href = res.msg;
      const windowOrigin = window.location.origin;
      const token = localStorage.getItem("token");
      let newHref = href;
      if (href.includes(windowOrigin)) {
        newHref = "/portal" + href.split(windowOrigin)[1];
      }
      window.open(windowOrigin + newHref + "?token=" + token);
    })
    .catch(() => {
      message.error("导出失败");
    });
};

const handleExportCancel = () => {
  showExportModal.value = false;
};
// const exportEx = () => {
//   let exportApi = exportApiMap[activeStatus.value];

//   if (!exportApi) {
//     return;
//   }

//   let params = {
//     category: processTypeInfo.value?.code,
//     ...searchParams.value,
//   };

//   exportApi(params)
//     .then((res) => {
//       const href = res.msg;
//       const windowOrigin = window.location.origin;
//       const token = localStorage.getItem("token");
//       let newHref = href;

//       if (href.includes(windowOrigin)) {
//         newHref = "/portal" + href.split(windowOrigin)[1];
//       }
//       window.open(windowOrigin + newHref + "?token=" + token);
//     })
//     .catch(() => {
//       message.error("导出失败");
//     });
// };

const applySupport = () => {
  // 处理申请逻辑
  nextTick(() => {
    showDispatchModal.value = true;
  });
};
const handleDispatchSelect = (type) => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const dispatchConfig = {
    售前调度: {
      to: "starWork",
      checkPermission: () => true,
    },
    售中调度: {
      to: "coordination",
      checkPermission: (userInfo) =>
        userInfo.roleKeyList.includes("deliveryManager"),
      errorMessage: "目前售中调度工单仅支持交付经理发起",
    },
    售后调度: {
      to: "",
      checkPermission: () => false,
      errorMessage: "售后调度工单流程暂未开放，敬请期待！",
    },
  };
  const config = dispatchConfig[type];
  if (!config) return;
  if (!config.checkPermission(userInfo)) {
    message.warning(config.errorMessage);
    return;
  }
  const params = {
    action: "edit",
    from: "center",
    to: config.to,
    active: "调度中心",
  };
  const searchParams = new URLSearchParams(params);
  window.location.replace(
    window.location.origin + "/#/dispatchCenter/transfer?" + searchParams.toString()
  );
};
watch(activeStatus, (newVal) => {
  searchParams.value = {};
  if (searchBarRef.value) {
    searchBarRef.value.reset();
  }
});
onMounted(() => {
  const params = {
    pageNum: 1,
    pageSize: 10,
  };
  getClassificationData(params)
    .then((res) => {
      if (res.data && res.data.rows) {
        processTypeInfo.value = res.data.rows.find(
          (item) => item.categoryName === "售前调度工单"
        );
      }
    })
    .catch(() => {
      message.error("获取分类数据异常");
    });
});
</script>

<style lang="scss" scoped>
.pre-sale-container {
  padding: 20px 0;
}
.toggle-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;

  .status-toggle {
    .ant-radio-button-wrapper {
      background: #ebf3fd;
      color: #3296fa; // 未选中文字
      border: none !important;
      // border-radius: 4px !important;
      &:hover {
        color: #1677ff;
        background: #e6f3ff;
      }
      &:first-child {
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
      }

      &:last-child {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
      }
    }
    // 选中状态
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      border-radius: 4px !important;
      color: #fff !important;
      background: linear-gradient(90deg, #1677ff 0%, #256cec 100%) !important;
      box-shadow: none;
    }
    .ant-radio-button-wrapper::before {
      display: none;
    }
  }

  .btn-group {
    display: flex;
    align-items: center;
    .default_btn {
      background: rgba(12, 112, 235, 0.08);
      border-radius: 4px 4px 4px 4px;
      color: #0c70eb;
      border-radius: 4px;
      border: none;
      margin-right: 16px;
    }

    .default_btn:hover {
      background: #d3e8ff !important;
      color: #0c70eb;
    }

    .default_btn:focus {
      background: rgba(12, 112, 235, 0.08) !important;
      color: #0c70eb;
    }

    .apply-support-btn {
      width: 120px;
    }
  }
}
.table-area {
  margin-top: 12px;
}
.ant-btn-primary {
  background: #0c70eb;
  border-radius: 4px;
  color: #fff !important;
  border: none;
}

.ant-btn-primary:hover {
  background: #509fff !important;
}

.ant-btn-primary:focus {
  background: #0c70eb !important;
}

.ant-switch-checked {
  background-color: #0c70eb;
}
</style>