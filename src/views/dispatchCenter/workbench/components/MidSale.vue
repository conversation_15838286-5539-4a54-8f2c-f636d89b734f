<template>
  <div class="mid-sale-container">
    <MidSearchBar
      ref="searchBarRef"
      @form-search="handleSearch"
      class="search-form"
    />
    <div class="toggle-actions">
      <a-radio-group
        v-model:value="activeStatus"
        button-style="solid"
        class="status-toggle"
      >
        <a-radio-button value="todo">待办</a-radio-button>
        <a-radio-button value="done">已办</a-radio-button>
        <a-radio-button value="all" v-if="isAdmin">全部</a-radio-button>
      </a-radio-group>

      <div class="btn-group">
        <a-button class="default_btn" @click="exportEx()">导出</a-button>
        <a-button type="primary" class="apply-support-btn" @click="applySupport" v-if="!isEcologicalPartner"
          >申请支撑</a-button
        >
      </div>
    </div>
    <div class="table-area">
      <component
        :is="currentComponent"
        :search-params="mergedSearchParams"
        :key="activeStatus"
      />
    </div>
    <DispatchTypeModal
      v-model="showDispatchModal"
      @select="handleDispatchSelect"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import waitWork from "../tables/waitWork.vue";
import finishWork from "../tables/finishWork.vue";
import midAll from "../tables/midAll.vue";
import MidSearchBar from "./MidSearchBar.vue";
import DispatchTypeModal from "@/components/DispatchTypeModal/index.vue";
import { getWorkOrderDown, exportTotal } from "@/api/ticket/ticket.js";
import { message } from "ant-design-vue";
const STORAGE_KEY_MID_ACTIVE_STATUS = "dispatch_center_mid_activeStatus";

const activeStatus = ref(
  sessionStorage.getItem(STORAGE_KEY_MID_ACTIVE_STATUS) || "todo"
);
watch(activeStatus, (newVal) => {
  sessionStorage.setItem(STORAGE_KEY_MID_ACTIVE_STATUS, newVal);
});
const searchParams = ref({});

const isAdmin = computed(() => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  return userInfo?.roleKeyList?.includes("admin");
});
const isEcologicalPartner = computed(() => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  return userInfo?.roleKeyList?.includes("ecologicalPartner");
});
// 调度中心弹窗显示状态
const showDispatchModal = ref(false);
const currentComponent = computed(() => {
  switch (activeStatus.value) {
    case "todo":
      return waitWork;
    case "done":
      return finishWork;
    case "all":
      return midAll;
    default:
      return waitWork;
  }
});
const mergedSearchParams = computed(() => ({
  ...searchParams.value,
  status:
    activeStatus.value === "todo"
      ? 0
      : activeStatus.value === "done"
      ? 1
      : undefined,
  type: 9,
}));

const searchBarRef = ref(null);

const handleSearch = (formData) => {
  searchParams.value = { ...formData };
};
const handleDispatchSelect = (type) => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const dispatchConfig = {
    售前调度: {
      to: "starWork",
      checkPermission: () => true,
    },
    售中调度: {
      to: "coordination",
      checkPermission: (userInfo) =>
        userInfo.roleKeyList.includes("deliveryManager"),
      errorMessage: "目前售中调度工单仅支持交付经理发起",
    },
    售后调度: {
      to: "",
      checkPermission: () => false,
      errorMessage: "售后调度工单流程暂未开放，敬请期待！",
    },
  };
  const config = dispatchConfig[type];
  if (!config) return;
  if (!config.checkPermission(userInfo)) {
    message.warning(config.errorMessage);
    return;
  }
  const params = {
    action: "edit",
    from: "center",
    to: config.to,
    active: "调度中心",
  };
  const searchParams = new URLSearchParams(params);
  window.location.replace(
    window.location.origin + "/#/dispatchCenter/transfer?" + searchParams.toString()
  );
};
watch(activeStatus, () => {
  searchParams.value = {};
  if (searchBarRef.value) searchBarRef.value.reset();
});
const exportEx = () => {
  if (activeStatus.value === "all") {
    exportTotal({ ...searchParams.value ,type: 9})
      .then((res) => {
        const href = res.msg;
        const windowOrigin = window.location.origin;
        const token = localStorage.getItem("token");
        let newHref = href;

        if (href.includes(windowOrigin)) {
          newHref = "/portal" + href.split(windowOrigin)[1];
        }
        window.open(windowOrigin + newHref + "?token=" + token);
      })
      .catch((err) => {
        console.error(err);
        message.error("导出失败");
      });
  } else {
    const params = {
      ...mergedSearchParams.value,
    };

    getWorkOrderDown(params)
      .then((res) => {
        const href = res.msg;
        const windowOrigin = window.location.origin;
        const token = localStorage.getItem("token");
        let newHref = href;

        if (href.includes(windowOrigin)) {
          newHref = "/portal" + href.split(windowOrigin)[1];
        }
        window.open(windowOrigin + newHref + "?token=" + token);
      })
      .catch((err) => {
        console.error(err);
      });
  }
};
const applySupport = () => {
  // 处理申请逻辑
  nextTick(() => {
    showDispatchModal.value = true;
  });
};
</script>

<style lang="scss" scoped>
.mid-sale-container {
  padding: 20px 0;
}

.toggle-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;

  .status-toggle {
    .ant-radio-button-wrapper {
      background: #ebf3fd;
      color: #3296fa; // 未选中文字
      border: none !important;
      // border-radius: 4px !important;

      &:hover {
        color: #1677ff;
        background: #e6f3ff;
      }
      &:first-child {
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
      }

      &:last-child {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
      }
    }

    // 选中状态
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      color: #fff !important;
      border-radius: 4px !important;
      background: linear-gradient(90deg, #1677ff 0%, #256cec 100%) !important;
      box-shadow: none;
    }

    .ant-radio-button-wrapper::before {
      display: none;
    }
  }

  .btn-group {
    display: flex;
    align-items: center;
    .default_btn {
      background: rgba(12, 112, 235, 0.08);
      border-radius: 4px 4px 4px 4px;
      color: #0c70eb;
      border-radius: 4px;
      border: none;
      margin-right: 16px;
    }

    .default_btn:hover {
      background: #d3e8ff !important;
      color: #0c70eb;
    }

    .default_btn:focus {
      background: rgba(12, 112, 235, 0.08) !important;
      color: #0c70eb;
    }

    .apply-support-btn {
      width: 120px;
    }
  }
}

.table-area {
  margin-top: 12px;
}

.ant-btn-primary {
  background: #0c70eb;
  border-radius: 4px;
  color: #fff !important;
  border: none;
}

.ant-btn-primary:hover {
  background: #509fff !important;
}

.ant-btn-primary:focus {
  background: #0c70eb !important;
}

.ant-switch-checked {
  background-color: #0c70eb;
}
</style>