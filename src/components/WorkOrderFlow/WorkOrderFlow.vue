<template>
  <div class="work-order-flow">
    <!-- Work Order Flow Content will be extracted here -->
    <div v-if="formLoading" class="loading-overlay">
      <a-spin :spinning="formLoading" tip="数据加载中..." />
    </div>

    <!-- Module Forms Section -->
    <div v-for="(moduleItem, moduleIndex) in moduleForm" :key="moduleItem.uid">
      <div class="module_title">
        <p class="weight500 font_14 font_00060e" style="margin-bottom:0">支撑模块{{ moduleIndex + 1 }}</p>
        <el-button v-if="moduleForm.length > 1 && action == 'edit'" link type="danger" size="small"
          htmlType="button" @click="handleDeleteModule(moduleIndex)">删除模块</el-button>
      </div>

      <div class="module_group">
        <!-- Province support selection -->
        <div
          v-if="supportType == '省级' && (moduleItem.status == 'toSelect' || moduleItem.status == 'reSelectPage') && !contralSupport"
          style="display: flex; padding-left: 24px; margin-bottom: 24px">
          <p style="margin-bottom: 0;color: rgba(0, 0, 0, 0.85);font-size: 14px;font-weight: 500;">是否自己支撑：</p>
          <a-radio-group v-model:value="moduleItem.isBySelf">
            <a-radio value="1">是</a-radio>
            <a-radio value="2">否</a-radio>
          </a-radio-group>
        </div>

        <!-- Province dispatch support -->
        <div
          v-if="((contralSupport || supportType == '市级') && (moduleItem.status == 'toSelect' || (moduleItem.status == 'reSelectPage' && moduleItem.textList.some(text => text.dealType == '1' || text.dealType == '2'))))"
          style="display: flex;margin-bottom: 24px;height: 32px;">
          <div class="provinceBtn" style="margin-right: 46px; align-items: center">
            <p style="margin-bottom: 0;color: rgba(0, 0, 0, 0.85);font-size: 14px;font-weight: 500;">申请省级调度支撑：</p>
            <a-switch v-model:checked="moduleItem.needProvince" />
          </div>
          <div v-if="moduleItem.needProvince" style="display: flex; margin-bottom: 0" class="select_pro">
            <p style="margin-bottom: 0; margin-right: 12px">选择省级调度人</p>
            <a-select placeholder="请选择调度人" v-model:value="moduleItem.provinceUser" allowClear>
              <template v-for="opt in provincePersonList" :key="opt.id">
                <a-select-option :value="opt.id">
                  {{ opt.realName }}
                </a-select-option>
              </template>
            </a-select>
          </div>
        </div>

        <!-- Support requirement description -->
        <a-row v-if="action == 'edit'">
          <a-col :span="24">
            <a-form-item label="支撑需求描述：">
              <a-textarea :rows="7" :showCount="true" :maxlength="500"
                placeholder="包含项目地点，具体需要支撑的内容，内容中切勿透露完整的项目名称和客户基本信息等，防止商机泄露。限制500个字以内"
                v-model:value="moduleItem.projectContent"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-else>
          <a-col :span="24">
            <a-form-item label="支撑需求描述：">
              {{ moduleItem.projectContent || '-' }}
            </a-form-item>
          </a-col>
        </a-row>

        <!-- Business module selection -->
        <a-form-item v-if="action == 'edit'" label="业务模块选择：">
          <a-radio-group v-model:value="moduleItem.businessModuleType">
            <a-radio value="1">解决方案</a-radio>
            <a-radio value="2">场景方案</a-radio>
            <a-radio value="3">原子能力</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- Module information -->
        <div>
          <p style="color: rgba(0, 0, 0, 0.85);font-size: 14px;font-weight: 500;">{{
            businessModuleTypeComputed(moduleItem.businessModuleType) }}信息：</p>
          <div class="abInfo no-margin-left-right">
            <div class="top">
              <span style="width:107px;display: inline-block;text-align: right;"
                class="weight500 font_14 font_00060e">
                {{ businessModuleTypeComputed(moduleItem.businessModuleType) }}名称：</span>
              <template v-if="action == 'edit'">
                <a-input disabled v-model:value="moduleItem.name"
                  :placeholder="'请选择' + businessModuleTypeComputed(moduleItem.businessModuleType)"
                  style="width: 248px" allowClear></a-input>
                <a-button style="margin-left: 10px;" class="custom_btn active_btn"
                  @click="() => showModuleTypeDialog(moduleIndex)">选择</a-button>
              </template>
              <span v-else>
                {{ moduleItem.name || '-' }}
              </span>
            </div>
            <div class="center">
              <span style="width:107px;margin-top: 24px;text-align: right;vertical-align: top;"
                class="weight500 font_14 font_00060e">
                {{ businessModuleTypeComputed(moduleItem.businessModuleType) }}简介：
              </span>
              <p style="margin-left: 4px;margin-top: 24px; width: calc(100% - 120px); white-space: pre-wrap;">{{
                moduleItem.intro }}</p>
            </div>
          </div>
        </div>

        <!-- Module status and node information -->
        <p v-if="action != 'edit'" class="group_title weight500 font_14 font_00060e" style="margin-top:20px">
          模块流程节点：
          <span style="font-weight:normal;">{{ moduleItem.nodeName }}</span>
        </p>
        <p v-if="action != 'dealedAll' && moduleItem.status == 'reStar'"
          class="group_title weight500 font_14 font_00060e" style="margin-top:20px">驳回理由：
          <span style="font-weight:normal;color:red;">{{ moduleItem.auditReason }}</span>
        </p>

        <!-- Action buttons -->
        <div v-if="moduleItem.status != 'selectApply' && action != 'dealedAll'" class="flex just-center"
          style="margin-top:10px;">
          <a-button v-if="moduleItem.status == 'toSelect' || moduleItem.status == 'reSelectPage'"
            class="margin_r_10 custom_btn reject_btn" @click="handleReject(moduleIndex)">驳回</a-button>
          <a-button v-if="action != 'edit' && moduleItem.status != 'reStar' && moduleItem.status != 'submitPage'"
            class="custom_btn active_btn" @click="submit(moduleIndex)" :loading="addLoading">确定</a-button>
          <a-button v-if="(moduleItem.status == 'reStar' || moduleItem.status == 'submitPage')"
            class="margin_r_10 custom_btn cancel_btn" @click="endWork(moduleIndex)">结束</a-button>
          <a-button v-if="moduleItem.status == 'reStar'" class="custom_btn active_btn"
            @click="reSubmit(moduleIndex)" :loading="addLoading">重新提交</a-button>
        </div>
      </div>
    </div>

    <!-- Add Module Button -->
    <div v-if="action == 'edit'" style="width: 100%;text-align: center;margin: 20px 0 0 0 ;">
      <a-button class="custom_btn active_btn" @click="addModule">新增支撑模块</a-button>
    </div>

    <!-- Flow Memo Section -->
    <p v-if="action != 'edit' && isCreatedByMyself" class="group_title weight500 font_14 font_00060e margin_t_24">
      <span class="icon"></span>流程备忘录
    </p>
    <flow-memo v-if="action != 'edit' && isCreatedByMyself" :order-id="orderId"
      v-model:order-comments="orderComments"></flow-memo>

    <!-- Work Order Flow Table -->
    <p v-if="action != 'edit'" class="group_title weight500 font_14 font_00060e margin_t_24">
      <span class="icon"></span>工单流程
    </p>
    <div style="padding: 0 24px">
      <el-table v-if="action != 'edit'" class="resize-table-header-line" :data="tableDataWork"
        :empty-text="'暂无数据'" border :header-cell-style="{ textAlign: 'center' }"
        style="width: 100%; margin-top: 20px">
        <el-table-column align="center" prop="activityName" label="工单流程" width="180" />
        <el-table-column align="center" prop="assigneeName" label="处理人">
          <template #default="scope">
            {{ scope.row.assigneeName }}
            {{ scope.row.orgName ? '(' + scope.row.orgName + ')' : '' }}
            {{ scope.row.phone }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="dealContent" label="处理内容">
          <template #default="scope">
            <el-tooltip v-if="scope.row.dealContent && scope.row.dealContent.length > 60" trigger="hover"
              popper-class="custom-tooltip" :content="scope.row.dealContent" placement="top">
              <p v-if="scope.row.dealContent !== ''" class="content_control">
                {{ scope.row.dealContent }}
              </p>
            </el-tooltip>
            <p v-if="!scope.row.dealContent">-</p>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="endTime" label="处理时间" />
      </el-table>
    </div>

    <!-- Modals -->
    <!-- Support feedback modal -->
    <a-modal v-model:visible="showSuggest" title="支撑反馈" :footer="null" width="50%">
      <a-form :model="formDataDeal" labelAlign="right" :rules="dealRules" class="operation">
        <a-form-item v-if="!refuseOpen && !isThirdEco" label="支撑人/天数：" name="supportCos">
          <a-input v-model:value="formDataDeal.supportCos" placeholder="请填写支撑人/天数"></a-input>
        </a-form-item>
        <a-form-item :label="refuseOpen ? '拒绝原因：' : '实际支撑结果：'" name="suggest">
          <a-textarea :rows="7" :showCount="true" :maxlength="refuseOpen ? 100 : 500"
            :placeholder="'请输入' + (refuseOpen ? '拒绝原因，限制100个字符' : '实际支撑结果内容，限制500个字符')"
            v-model:value="formDataDeal.suggest"></a-textarea>
        </a-form-item>
      </a-form>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleCloseSugget"> 取消 </a-button>
        <a-button class="custom_btn active_btn" @click="$emit('suggestConfirm', formDataDeal)"> 确认 </a-button>
      </div>
    </a-modal>

    <!-- Score modal -->
    <a-modal v-model:visible="showScore" title="提示" :footer="null" width="40%">
      <a-form :model="formDataScore" labelAlign="right" :rules="rulesScore">
        <a-row>
          <a-col :span="24">
            <a-form-item label="是否转售中" name="isTurn">
              <a-radio-group v-model:value="formDataScore.isTurn" @change="changeRadio">
                <a-radio value="1">是</a-radio>
                <a-radio value="2">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="项目名称" name="projectName">
              <a-input v-model:value="formDataScore.projectName" placeholder="请填写DICT项目管理系统项目名称"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="项目编码" name="projectCode">
              <a-input type="number" v-model:value="formDataScore.projectCode"
                placeholder="请填写DICT项目管理系统项目编码"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="isSubmit">
          <a-col :span="24">
            <a-form-item label="交付经理" name="deliveryManager">
              <a-select placeholder="请选择交付经理" v-model:value="formDataScore.deliveryManager" allowClear show-search>
                <template v-for="(opt, index) in personListNew" :key="index">
                  <a-select-option :value="String(opt.id)" :label="opt.realName">
                    {{ opt.realName }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div style="display: flex; padding-left: 65px; color: #a2abb5">
        <p>备注：</p>
        <p>选择是将发送短信给交付经理</p>
      </div>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleCloseScore"> 取消 </a-button>
        <a-button class="custom_btn active_btn" @click="$emit('scoreConfirm', formDataScore)"> 确认 </a-button>
      </div>
    </a-modal>

    <!-- Suggestion modal -->
    <a-modal v-model:visible="showSuggestCity" title="处理意见" :footer="null" width="50%">
      <a-textarea :rows="7" :showCount="true" :maxlength="500" placeholder="请输入处理意见，限制500个字符"
        v-model:value="suggest"></a-textarea>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleCloseSuggeOld"> 取消 </a-button>
        <a-button class="custom_btn active_btn" @click="$emit('rejectConfirm', suggest)"> 确认 </a-button>
      </div>
    </a-modal>

    <!-- Module selection modal -->
    <a-modal :visible="moduleTypeVisible" @cancel="closeModal"
      :title="'选择' + businessModuleTypeComputed(moduleForm[currentModuleIndex]?.businessModuleType)"
      width="1200px" centered :destroyOnClose="true" :maskClosable="false" :footer="null">
      <!-- Module selection content will be handled by parent -->
      <slot name="moduleSelection" :moduleIndex="currentModuleIndex"></slot>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, computed } from "vue";
import { message } from "ant-design-vue";
import FlowMemo from "./FlowMemo.vue";
import {
  batchSubmitWithdrawProcess,
  cityBack,
  midDispatch
} from "@/api/processManage/backlog&completed/index.js";
import {
  operateOrderById,
  querytOrderById
} from "@/api/processManage/index.js";

export default defineComponent({
  name: "WorkOrderFlow",
  components: {
    FlowMemo
  },
  props: {
    // Configuration props
    action: {
      type: String,
      default: 'edit'
    },
    orderId: {
      type: [String, Number],
      required: true
    },
    // Data props
    moduleForm: {
      type: Array,
      default: () => []
    },
    orderComments: {
      type: Array,
      default: () => []
    },
    tableDataWork: {
      type: Array,
      default: () => []
    },
    // State props
    formLoading: {
      type: Boolean,
      default: false
    },
    addLoading: {
      type: Boolean,
      default: false
    },
    isCreatedByMyself: {
      type: Boolean,
      default: false
    },
    // Workflow configuration
    supportType: {
      type: String,
      default: '市级'
    },
    contralSupport: {
      type: Boolean,
      default: false
    },
    userInfo: {
      type: Object,
      default: () => ({})
    }
  },
  emits: [
    'update:moduleForm',
    'update:orderComments',
    'submit',
    'reject',
    'endWork',
    'reSubmit',
    'addModule',
    'deleteModule',
    'addCooperate',
    'checkChange',
    'selectModule',
    'scoreConfirm',
    'suggestConfirm'
  ],
  setup(props, { emit }) {
    const data = reactive({
      // Workflow state
      operateIndex: 0, // 待操作的模块索引
      showScore: false,
      showSuggestCity: false,
      isSubmit: false,

      // Form data for scoring
      formDataScore: {
        isTurn: "2",
        projectName: "",
        projectCode: "",
        deliveryManager: "",
      },

      // Form data for dealing with feedback
      formDataDeal: {
        suggest: "",
        supportCos: "",
        fileListDeal: [],
      },

      // UI state
      refuseOpen: false,
      suggest: "", // 处理意见
      showSuggest: false, // 是否显示处理意见弹框
      isThirdEco: false, // 是否生态三方厂商
      companyAload: true,

      // Company and user data
      rejectCompanyIdlist: [], // 厂商拒绝后调度人页面重新选择厂商数据
      reSelectCompany: {}, // 厂商拒绝后调度人页面重新选择厂商数据
      dataCompanyNew: [],

      // Modal visibility
      showAdd: false,
      moduleTypeVisible: false,
      currentModuleIndex: 0,

      // Cooperation data
      CooperateData: {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        area: undefined,
        enterpriseId: undefined,
        projectCode: undefined,
      },

      // Lists and options
      contanctList: [],
      teamOldList: [],
      personListNew: [],
      provincePersonList: [],

      // Validation rules
      dealRules: {
        suggest: [
          {
            required: true,
            message: "请输入实际支撑结果",
            trigger: "change",
          },
        ],
        supportCos: [
          {
            required: true,
            message: "请输入支撑人/天数",
            trigger: "change",
          },
        ],
      },

      rulesScore: {
        isTurn: [
          {
            required: true,
            message: "请选择是否流转",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        projectCode: [{ required: true, message: "项目编码不能为空" }],
        deliveryManager: [
          {
            required: true,
            message: "请选择交付负责人",
            trigger: "change",
          },
        ],
      },
    });

    // Methods
    const handleDeleteModule = (index) => {
      emit('deleteModule', index);
    };

    const handleReject = (index) => {
      data.operateIndex = index;
      emit('reject', index);
    };

    const submit = (index) => {
      data.operateIndex = index;
      emit('submit', index);
    };

    const endWork = (index) => {
      data.operateIndex = index;
      emit('endWork', index);
    };

    const reSubmit = (index) => {
      data.operateIndex = index;
      emit('reSubmit', index);
    };

    const addModule = () => {
      emit('addModule');
    };

    // Business module type computed
    const businessModuleTypeComputed = (type) => {
      if (type == 1) return '方案';
      if (type == 2) return '场景方案';
      if (type == 3) return '能力';
      return '';
    };

    // Handle modal operations
    const closeModal = () => {
      data.moduleTypeVisible = false;
    };

    const showModuleTypeDialog = (index) => {
      data.moduleTypeVisible = true;
      data.currentModuleIndex = index;
    };

    // Handle scoring modal
    const handleCloseScore = () => {
      data.showScore = false;
      data.formDataScore.deliveryManager = undefined;
      data.formDataScore.isTurn = "2";
      data.isSubmit = false;
    };

    const changeRadio = (e) => {
      if (e.target.value == "1") {
        data.isSubmit = true;
      } else {
        data.isSubmit = false;
        data.formDataScore.deliveryManager = undefined;
      }
    };

    // Handle suggestion modal
    const handleCloseSugget = () => {
      data.showSuggest = false;
      data.formDataDeal.fileListDeal = [];
      data.formDataDeal.suggest = "";
      data.formDataDeal.supportCos = "";
      data.companyAload = true;
    };

    const handleCloseSuggeOld = () => {
      data.showSuggestCity = false;
      data.companyAload = !data.companyAload;
    };

    // Company operations
    const submitCompany = () => {
      data.showSuggest = true;
      data.companyAload = true; // 同意
      data.formDataDeal.suggest = "";
      data.refuseOpen = false;
      data.isThirdEco = false; // This should be passed as prop or computed
    };

    const refuseApplyCompany = () => {
      data.showSuggest = true;
      data.companyAload = false; // 拒绝
      data.formDataDeal.suggest = "";
      data.refuseOpen = true;
    };

    // Add cooperation
    const addCooperate = (value, index) => {
      data.currentModuleIndex = index;
      data.showAdd = true;
      emit('addCooperate', { value, index });
    };

    const closeAdd = () => {
      data.showAdd = false;
      data.contanctList = [];
      data.CooperateData = {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        area: undefined,
        enterpriseId: undefined,
      };
    };

    // Radio change for company selection
    const onCheckChange = (e, item, index, type) => {
      emit('checkChange', { e, item, index, type });
    };

    return {
      ...toRefs(data),
      handleDeleteModule,
      handleReject,
      submit,
      endWork,
      reSubmit,
      addModule,
      businessModuleTypeComputed,
      closeModal,
      showModuleTypeDialog,
      handleCloseScore,
      changeRadio,
      handleCloseSugget,
      handleCloseSuggeOld,
      submitCompany,
      refuseApplyCompany,
      addCooperate,
      closeAdd,
      onCheckChange
    };
  }
});
</script>

<style lang="scss" scoped>
// Work Order Flow Component Styles
.work-order-flow {
  width: 100%;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}

// Module styles
.module_group {
  padding: 24px;
  margin: 0 24px 0 32px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #D1D0D8;
}

.module_group:not(:first-child) {
  margin-top: 20px;
}

.module_title {
  display: flex;
  margin: 10px 0 10px 32px;
  justify-content: space-between;
  align-items: center;
}

// Button styles
.custom_btn {
  color: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  border: none;
}

.reject_btn {
  background: linear-gradient(90deg, #FF845F 0%, #FF3730 100%);
}

.active_btn {
  background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
}

.cancel_btn {
  background: rgba(12, 112, 235, 0.08);
  color: #0C70EB;
}

.custom_btn:hover,
.custom_btn:focus {
  opacity: 0.6;
}

// Icon and title styles
.icon {
  display: inline-block;
  width: 4px;
  height: 13px;
  background: #0c70eb;
  box-shadow: 2px 1px 6px 0px rgba(12, 112, 235, 0.5),
      inset 0px 0px 3px 0px rgba(255, 255, 255, 0.8);
  border-radius: 2px 2px 2px 2px;
  margin-right: 8px;
}

.group_title {
  margin: 10px 0 10px 32px;
}

// Content control
.content_control {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Province support styles
.provinceBtn {
  display: flex;

  p {
    display: inline-block;
    margin-right: 10px;
  }
}

.select_pro {
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 340px;
  }
}

// Module info styles
.abInfo {
  margin: 10px 24px 10px 32px;
  padding: 24px;
  border: 1px solid #D1D0D8;

  .top {
    p {
      display: inline-block;
    }

    .tit {
      font-weight: 700;
    }
  }

  .center {
    display: flex;
    align-items: start;
    flex-wrap: wrap;

    p {
      display: inline-block;
      width: 24%;
    }
  }
}

.no-margin-left-right {
  margin-left: 0;
  margin-right: 0;
}

// Form styles
:deep(.ant-form-item-label) {
  width: 107px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}

:deep(.ant-input) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

:deep(textarea) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

.ant-input:focus {
  box-shadow: none !important;
}

:deep(textarea:focus) {
  box-shadow: none;
}

// Radio and checkbox styles
:deep(.ant-radio-group) {
  text-align: left;
}

.ant-radio-group {
  display: flex;
  align-items: center;
}

// Table styles
.resize-table-header-line.el-table {
  th.el-table__cell,
  td>.cell {
    cursor: default !important;
  }

  th.el-table__cell {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 8px;
      height: 100%;
      cursor: default;
      transform: translateX(50%);
    }

    &:hover::after {
      cursor: col-resize !important;
    }
  }
}

// Tooltip styles
.custom-tooltip {
  color: #fff !important;
  max-width: 600px;
  background: rgba(96, 98, 102, 0.9) !important;
}

// Utility classes
.flex {
  display: flex;
}

.just-center {
  justify-content: center;
}

.just-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.margin_r_10 {
  margin-right: 10px;
}

.margin_t_24 {
  margin-top: 24px;
}

.weight500 {
  font-weight: 500;
}

.font_14 {
  font-size: 14px;
}

.font_00060e {
  color: rgba(0, 6, 14, 0.6);
}
</style>
