/*
 * @Description:
 * @Author: xiuji
 * @Date: 2024-03-25 09:56:59
 * @LastEditTime: 2024-03-27 16:22:19
 * @LastEditors: Do not edit
 */
import {
    createApp
} from 'vue'
import App from './App.vue'
import router from './router/index'
import store from './store/index'
import Antd from 'ant-design-vue'
import ElementPlus from 'element-plus'
import VueLazyload from 'vue-lazyload';
import MarkdownIt from 'markdown-it'
import 'element-plus/dist/index.css'
// import '@unocss/reset/tailwind.css'
import "ant-design-vue/dist/antd.css";
import 'animate.css';
import "@/assets/iconfont/iconfont.css"
import "@/assets/iconfont/iconfont.js"
import '@/assets/css/_font.scss'
import '@/assets/css/_layout.scss'
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
// 引入打印插件
import print from "vue3-print-nb";
// highlightjs
import hljs from 'highlight.js';

VMdPreview.use(githubTheme, {
  Hljs: hljs,
});

import('@/assets/css/index.scss').then(() => {
    createApp(App).use(router).use(store).use(Antd).use(print).use(VMdPreview).use(MarkdownIt).use(ElementPlus).use(VueLazyload, {
        // loading: 'https://smart.jsisi.cn:8099/portal/resource/2025/01/06/200b0fc7-33bd-4133-955e-ecc1fc91dd89/loading.gif', // 加载中时的图片
    }).mount('#app')
})

// Vue.use(VueLazyload, {
//     preLoad: 1.3, // 预加载高度比例，默认为1.3
//     error: 'path/to/error.png', // 加载失败时的图片
//     loading: 'path/to/loading.gif', // 加载中时的图片
//     attempt: 1 // 尝试加载图片的次数，默认为1
//   });
